package cli

import (
	"flag"
	"fmt"
	"os"
	"strings"
)

// ParseFlags processes command-line arguments and returns a validated Config structure.
// It enforces required parameters for both training and prediction operations and
// supports advanced decision tree configuration options.
//
// The function handles:
//   - Core command flags (command, input, output, etc.)
//   - Decision tree parameters (max-depth, min-samples, criterion)
//   - Special flags (help, version, verbose)
//   - Comprehensive validation of all parameters
//
// Returns a fully validated Config or an error with specific context.
func ParseFlags() (*Config, error) {
	config := &Config{}

	// Core command flags
	flag.StringVar(&config.Command, "c", "", "Specify the command (train or predict)")
	flag.StringVar(&config.InputFile, "i", "", "Path to the input CSV file")
	flag.StringVar(&config.TargetCol, "t", "", "Name of the target column (for training only)")
	flag.StringVar(&config.OutputFile, "o", "", "Path to save the output (trained model or predictions)")
	flag.StringVar(&config.ModelFile, "m", "", "Path to the trained model file (for prediction only)")
	flag.StringVar(&config.FeatureInfoFile, "f", "", "Path to the feature info file containing feature descriptions")
	flag.StringVar(&config.LogConfigPath, "log-config", "", "Path to the logger configuration file")

	// Decision tree parameters with defaults
	var maxDepth = flag.Int("max-depth", 10, "Maximum depth of the decision tree")
	var minSamplesSplit = flag.Int("min-samples", 2, "Minimum samples required to split a node")
	var minSamplesLeaf = flag.Int("min-leaf", 1, "Minimum samples required at a leaf node")
	var criterion = flag.String("criterion", "entropy", "Splitting criterion (gini, entropy, mse)")

	// CLI options
	var verbose = flag.Bool("verbose", false, "Enable verbose output")
	var help = flag.Bool("help", false, "Show help message")
	var version = flag.Bool("version", false, "Show version information")

	// Custom usage function
	flag.Usage = func() {
		config.ShowHelp()
	}

	flag.Parse()

	// Handle special flags first
	if *help {
		config.ShowHelp()
		os.Exit(0)
	}

	if *version {
		config.ShowVersion()
		os.Exit(0)
	}

	// Set parsed values
	config.MaxDepth = *maxDepth
	config.MinSamplesSplit = *minSamplesSplit
	config.MinSamplesLeaf = *minSamplesLeaf
	config.Criterion = strings.ToLower(strings.TrimSpace(*criterion))
	config.Verbose = *verbose

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// ValidateArgs performs additional validation on command-line arguments.
// This can be used to check for conflicting flags or invalid combinations.
func ValidateArgs() error {
	args := flag.Args()

	// Check for unexpected positional arguments
	if len(args) > 0 {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("unexpected positional arguments: %v", args),
		}
	}

	return nil
}

