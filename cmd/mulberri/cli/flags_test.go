package cli

import (
	"errors"
	"flag"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseFlags(t *testing.T) {
	// Create temporary files for testing
	tmpInputFile, err := os.CreateTemp("", "input*.csv")
	require.NoError(t, err)
	defer os.Remove(tmpInputFile.Name())

	tmpModelFile, err := os.CreateTemp("", "model*.dt")
	require.NoError(t, err)
	defer os.Remove(tmpModelFile.Name())

	tmpMetadataFile, err := os.CreateTemp("", "metadata*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpMetadataFile.Name())

	// Create a temporary file with wrong extension for testing
	tmpWrongExtFile, err := os.CreateTemp("", "wrong*.txt")
	require.NoError(t, err)
	defer os.Remove(tmpWrongExtFile.Name())

	// Save the original os.Args
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	tests := []struct {
		name        string
		args        []string
		wantConfig  *Config
		wantErr     bool
		errType     string
		errContains string
	}{
		{
			name: "valid train command with defaults",
			args: []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target", "-o", "model.dt"},
			wantConfig: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
				Verbose:         false,
			},
			wantErr: false,
		},
		{
			name: "valid train command with custom parameters",
			args: []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target", "-o", "model.dt",
				"-f", tmpMetadataFile.Name(), "--max-depth", "5", "--min-samples", "10", "--criterion", "entropy", "--verbose"},
			wantConfig: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: tmpMetadataFile.Name(),
				MaxDepth:        5,
				MinSamplesSplit: 10,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
				Verbose:         true,
			},
			wantErr: false,
		},
		{
			name: "valid predict command",
			args: []string{"prog", "-c", "predict", "-i", tmpInputFile.Name(), "-m", tmpModelFile.Name(), "-o", "predictions.csv"},
			wantConfig: &Config{
				Command:         "predict",
				InputFile:       tmpInputFile.Name(),
				ModelFile:       tmpModelFile.Name(),
				OutputFile:      "predictions.csv",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
				Verbose:         false,
			},
			wantErr: false,
		},
		{
			name:        "missing command",
			args:        []string{"prog", "-i", tmpInputFile.Name()},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "command not specified",
		},
		{
			name:        "invalid command",
			args:        []string{"prog", "-c", "invalid", "-i", tmpInputFile.Name()},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid command",
		},
		{
			name:        "missing input file",
			args:        []string{"prog", "-c", "train"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "input file not specified",
		},
		{
			name:        "non-existent input file",
			args:        []string{"prog", "-c", "train", "-i", "nonexistent.csv"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "file does not exist",
		},
		{
			name:        "invalid input file extension",
			args:        []string{"prog", "-c", "train", "-i", tmpWrongExtFile.Name(), "-t", "target", "-o", "model.dt"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid file extension",
		},
		{
			name:        "missing target column for training",
			args:        []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-o", "model.dt"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "target column not specified",
		},
		{
			name:        "missing output path for training",
			args:        []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "output path not specified",
		},
		{
			name:        "missing model file for prediction",
			args:        []string{"prog", "-c", "predict", "-i", tmpInputFile.Name(), "-o", "predictions.csv"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "model file not specified",
		},
		{
			name:        "non-existent model file",
			args:        []string{"prog", "-c", "predict", "-i", tmpInputFile.Name(), "-m", "nonexistent.dt", "-o", "predictions.csv"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "model file does not exist",
		},
		{
			name:        "missing output path for prediction",
			args:        []string{"prog", "-c", "predict", "-i", tmpInputFile.Name(), "-m", tmpModelFile.Name()},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "output path not specified",
		},
		{
			name:        "invalid max depth",
			args:        []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target", "-o", "model.dt", "--max-depth", "0"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "max depth must be positive",
		},
		{
			name:        "invalid min samples split",
			args:        []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target", "-o", "model.dt", "--min-samples", "1"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "min samples split must be >= 2",
		},
		{
			name:        "invalid criterion",
			args:        []string{"prog", "-c", "train", "-i", tmpInputFile.Name(), "-t", "target", "-o", "model.dt", "--criterion", "invalid"},
			wantConfig:  nil,
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid criterion",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the command-line arguments for this test
			os.Args = tt.args

			// Reset the flag package to parse the new arguments
			flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ContinueOnError)

			gotConfig, err := ParseFlags()

			// Check if error expectation matches
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseFlags() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If we expect an error, check its type and content
			if tt.wantErr && err != nil {
				if tt.errType != "" {
					// Check if error is a CLIError (direct or wrapped)
					var cliErr *CLIError
					if errors.As(err, &cliErr) {
						assert.IsType(t, &CLIError{}, cliErr, "Error should be CLIError type")
					} else {
						t.Errorf("Expected CLIError but got %T", err)
					}
				}
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				}
				return
			}

			// Check the returned config
			if tt.wantConfig != nil {
				assert.Equal(t, tt.wantConfig.Command, gotConfig.Command, "Command should match")
				assert.Equal(t, tt.wantConfig.InputFile, gotConfig.InputFile, "InputFile should match")
				assert.Equal(t, tt.wantConfig.TargetCol, gotConfig.TargetCol, "TargetCol should match")
				assert.Equal(t, tt.wantConfig.OutputFile, gotConfig.OutputFile, "OutputFile should match")
				assert.Equal(t, tt.wantConfig.ModelFile, gotConfig.ModelFile, "ModelFile should match")
				assert.Equal(t, tt.wantConfig.FeatureInfoFile, gotConfig.FeatureInfoFile, "FeatureInfoFile should match")
				assert.Equal(t, tt.wantConfig.MaxDepth, gotConfig.MaxDepth, "MaxDepth should match")
				assert.Equal(t, tt.wantConfig.MinSamplesSplit, gotConfig.MinSamplesSplit, "MinSamplesSplit should match")
				assert.Equal(t, tt.wantConfig.MinSamplesLeaf, gotConfig.MinSamplesLeaf, "MinSamplesLeaf should match")
				assert.Equal(t, tt.wantConfig.Criterion, gotConfig.Criterion, "Criterion should match")
				assert.Equal(t, tt.wantConfig.Verbose, gotConfig.Verbose, "Verbose should match")
			}
		})
	}
}

func TestConfigValidation(t *testing.T) {
	// Create temporary files for testing
	tmpInputFile, err := os.CreateTemp("", "input*.csv")
	require.NoError(t, err)
	defer os.Remove(tmpInputFile.Name())

	tmpModelFile, err := os.CreateTemp("", "model*.dt")
	require.NoError(t, err)
	defer os.Remove(tmpModelFile.Name())

	tmpMetadataFile, err := os.CreateTemp("", "metadata*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpMetadataFile.Name())

	// Create a temporary file with wrong extension for testing
	tmpWrongExtFile, err := os.CreateTemp("", "wrong*.txt")
	require.NoError(t, err)
	defer os.Remove(tmpWrongExtFile.Name())

	tests := []struct {
		name        string
		config      *Config
		wantErr     bool
		errType     string
		errContains string
	}{
		{
			name: "valid training config",
			config: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
			wantErr: false,
		},
		{
			name: "valid prediction config",
			config: &Config{
				Command:         "predict",
				InputFile:       tmpInputFile.Name(),
				ModelFile:       tmpModelFile.Name(),
				OutputFile:      "predictions.csv",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
			wantErr: false,
		},
		{
			name: "valid config with metadata",
			config: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				FeatureInfoFile: tmpMetadataFile.Name(),
				MaxDepth:        5,
				MinSamplesSplit: 10,
				MinSamplesLeaf:  5,
				Criterion:       "entropy",
				Verbose:         true,
			},
			wantErr: false,
		},
		{
			name: "empty command",
			config: &Config{
				InputFile: tmpInputFile.Name(),
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "command not specified",
		},
		{
			name: "invalid command",
			config: &Config{
				Command:   "invalid",
				InputFile: tmpInputFile.Name(),
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid command",
		},
		{
			name: "non-existent input file",
			config: &Config{
				Command:   "train",
				InputFile: "nonexistent.csv",
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "file does not exist",
		},
		{
			name: "invalid file extension",
			config: &Config{
				Command:   "train",
				InputFile: tmpWrongExtFile.Name(),
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid file extension",
		},
		{
			name: "invalid max depth",
			config: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        0,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "max depth must be positive",
		},
		{
			name: "invalid min samples split",
			config: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        10,
				MinSamplesSplit: 1,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "min samples split must be >= 2",
		},
		{
			name: "invalid criterion",
			config: &Config{
				Command:         "train",
				InputFile:       tmpInputFile.Name(),
				TargetCol:       "target",
				OutputFile:      "model.dt",
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "invalid",
			},
			wantErr:     true,
			errType:     "*cli.CLIError",
			errContains: "invalid criterion",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if (err != nil) != tt.wantErr {
				t.Errorf("Config.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil {
				if tt.errType != "" {
					var cliErr *CLIError
					if errors.As(err, &cliErr) {
						assert.IsType(t, &CLIError{}, cliErr, "Error should be CLIError type")
					} else {
						t.Errorf("Expected CLIError but got %T", err)
					}
				}
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				}
			}
		})
	}
}

func TestCLIOptions(t *testing.T) {
	tests := []struct {
		name        string
		options     []CLIOption
		wantConfig  *Config
		wantErr     bool
		errContains string
	}{
		{
			name: "valid options",
			options: []CLIOption{
				WithMaxDepth(5),
				WithMinSamplesSplit(10),
				WithMinSamplesLeaf(3),
				WithCriterion("entropy"),
				WithVerbose(true),
			},
			wantConfig: &Config{
				MaxDepth:        5,
				MinSamplesSplit: 10,
				MinSamplesLeaf:  3,
				Criterion:       "entropy",
				Verbose:         true,
			},
			wantErr: false,
		},
		{
			name: "invalid max depth",
			options: []CLIOption{
				WithMaxDepth(0),
			},
			wantErr:     true,
			errContains: "max depth must be positive",
		},
		{
			name: "invalid min samples split",
			options: []CLIOption{
				WithMinSamplesSplit(1),
			},
			wantErr:     true,
			errContains: "min samples split must be >= 2",
		},
		{
			name: "invalid min samples leaf",
			options: []CLIOption{
				WithMinSamplesLeaf(0),
			},
			wantErr:     true,
			errContains: "min samples leaf must be >= 1",
		},
		{
			name: "invalid criterion",
			options: []CLIOption{
				WithCriterion("invalid"),
			},
			wantErr:     true,
			errContains: "invalid criterion",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := NewConfig(tt.options...)

			if (err != nil) != tt.wantErr {
				t.Errorf("NewConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && tt.errContains != "" {
				assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				return
			}

			if tt.wantConfig != nil {
				assert.Equal(t, tt.wantConfig.MaxDepth, config.MaxDepth, "MaxDepth should match")
				assert.Equal(t, tt.wantConfig.MinSamplesSplit, config.MinSamplesSplit, "MinSamplesSplit should match")
				assert.Equal(t, tt.wantConfig.MinSamplesLeaf, config.MinSamplesLeaf, "MinSamplesLeaf should match")
				assert.Equal(t, tt.wantConfig.Criterion, config.Criterion, "Criterion should match")
				assert.Equal(t, tt.wantConfig.Verbose, config.Verbose, "Verbose should match")
			}
		})
	}
}

func TestCLIError(t *testing.T) {
	tests := []struct {
		name        string
		err         *CLIError
		wantMessage string
	}{
		{
			name: "error with command and flag",
			err: &CLIError{
				Op:      "validation",
				Command: "train",
				Flag:    "input",
				Err:     fmt.Errorf("file not found"),
			},
			wantMessage: "CLI validation failed for command 'train' flag 'input': file not found",
		},
		{
			name: "error with command only",
			err: &CLIError{
				Op:      "parsing",
				Command: "predict",
				Err:     fmt.Errorf("invalid format"),
			},
			wantMessage: "CLI parsing failed for command 'predict': invalid format",
		},
		{
			name: "error without command",
			err: &CLIError{
				Op:  "initialization",
				Err: fmt.Errorf("system error"),
			},
			wantMessage: "CLI initialization failed: system error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.wantMessage, tt.err.Error(), "Error message should match")
			assert.Equal(t, tt.err.Err, tt.err.Unwrap(), "Unwrap should return underlying error")
		})
	}
}

// Property-based test for edge cases
func TestConfigValidationProperties(t *testing.T) {
	// Create a temporary CSV file for testing
	tmpFile, err := os.CreateTemp("", "test*.csv")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	// Test that any valid configuration should pass validation
	validConfigs := []*Config{
		{
			Command:         "train",
			InputFile:       tmpFile.Name(),
			TargetCol:       "target",
			OutputFile:      "model.dt",
			MaxDepth:        1,
			MinSamplesSplit: 2,
			MinSamplesLeaf:  1,
			Criterion:       "entropy",
		},
		{
			Command:         "train",
			InputFile:       tmpFile.Name(),
			TargetCol:       "target",
			OutputFile:      "model.dt",
			MaxDepth:        100,
			MinSamplesSplit: 50,
			MinSamplesLeaf:  25,
			Criterion:       "entropy",
		},
	}

	for i, config := range validConfigs {
		t.Run(fmt.Sprintf("valid_config_%d", i), func(t *testing.T) {
			err := config.Validate()
			assert.NoError(t, err, "Valid configuration should not produce validation errors")
		})
	}
}

func TestShowHelp(t *testing.T) {
	config := &Config{}
	// This should not panic and should output help text
	// We can't easily test the output, but we can ensure it doesn't crash
	config.ShowHelp()
}

func TestShowVersion(t *testing.T) {
	config := &Config{}
	// This should not panic and should output version info
	config.ShowVersion()
}

func TestValidateArgs(t *testing.T) {
	// Save original flag.Args
	originalArgs := os.Args
	defer func() { os.Args = originalArgs }()

	tests := []struct {
		name        string
		args        []string
		wantErr     bool
		errContains string
	}{
		{
			name:    "no positional arguments",
			args:    []string{"prog"},
			wantErr: false,
		},
		{
			name:        "unexpected positional arguments",
			args:        []string{"prog", "extra", "args"},
			wantErr:     true,
			errContains: "unexpected positional arguments",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset flag package
			flag.CommandLine = flag.NewFlagSet(tt.args[0], flag.ContinueOnError)
			os.Args = tt.args
			flag.Parse()

			err := ValidateArgs()

			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateArgs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && tt.errContains != "" {
				assert.Contains(t, err.Error(), tt.errContains)
			}
		})
	}
}

func TestValidateFeatureInfoFile(t *testing.T) {
	// Create temporary files
	tmpYamlFile, err := os.CreateTemp("", "metadata*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpYamlFile.Name())

	tmpYmlFile, err := os.CreateTemp("", "metadata*.yml")
	require.NoError(t, err)
	defer os.Remove(tmpYmlFile.Name())

	tmpWrongExtFile, err := os.CreateTemp("", "metadata*.txt")
	require.NoError(t, err)
	defer os.Remove(tmpWrongExtFile.Name())

	tests := []struct {
		name        string
		config      *Config
		wantErr     bool
		errContains string
	}{
		{
			name: "valid yaml file",
			config: &Config{
				Command:         "train",
				FeatureInfoFile: tmpYamlFile.Name(),
			},
			wantErr: false,
		},
		{
			name: "valid yml file",
			config: &Config{
				Command:         "train",
				FeatureInfoFile: tmpYmlFile.Name(),
			},
			wantErr: false,
		},
		{
			name: "non-existent metadata file",
			config: &Config{
				Command:         "train",
				FeatureInfoFile: "nonexistent.yaml",
			},
			wantErr:     true,
			errContains: "feature info file does not exist",
		},
		{
			name: "invalid feature info file extension",
			config: &Config{
				Command:         "train",
				FeatureInfoFile: tmpWrongExtFile.Name(),
			},
			wantErr:     true,
			errContains: "invalid feature info file extension",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.validateFeatureInfoFile()

			if (err != nil) != tt.wantErr {
				t.Errorf("validateFeatureInfoFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && tt.errContains != "" {
				assert.Contains(t, err.Error(), tt.errContains)
			}
		})
	}
}

func TestValidateInputFileEdgeCases(t *testing.T) {
	// Create a directory for testing
	tmpDir, err := os.MkdirTemp("", "testdir")
	require.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	// Create a directory with .csv extension to test the directory check
	csvDir := tmpDir + ".csv"
	err = os.Mkdir(csvDir, 0755)
	require.NoError(t, err)
	defer os.RemoveAll(csvDir)

	tests := []struct {
		name        string
		config      *Config
		wantErr     bool
		errContains string
	}{
		{
			name: "input path is directory with csv extension",
			config: &Config{
				Command:   "train",
				InputFile: csvDir,
			},
			wantErr:     true,
			errContains: "path is a directory, not a file",
		},
		{
			name: "input path is directory without csv extension",
			config: &Config{
				Command:   "train",
				InputFile: tmpDir,
			},
			wantErr:     true,
			errContains: "invalid file extension",
		},
		{
			name: "empty input file",
			config: &Config{
				Command:   "train",
				InputFile: "",
			},
			wantErr:     true,
			errContains: "input file not specified",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.validateInputFile()

			if (err != nil) != tt.wantErr {
				t.Errorf("validateInputFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && tt.errContains != "" {
				assert.Contains(t, err.Error(), tt.errContains)
			}
		})
	}
}

func TestCLIErrorEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		err         *CLIError
		wantMessage string
		wantUnwrap  error
	}{
		{
			name: "error without command or flag",
			err: &CLIError{
				Op:  "initialization",
				Err: fmt.Errorf("system error"),
			},
			wantMessage: "CLI initialization failed: system error",
			wantUnwrap:  fmt.Errorf("system error"),
		},
		{
			name: "error with nil underlying error",
			err: &CLIError{
				Op:      "validation",
				Command: "train",
				Flag:    "input",
				Err:     nil,
			},
			wantMessage: "CLI validation failed for command 'train' flag 'input': <nil>",
			wantUnwrap:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.wantMessage, tt.err.Error())
			assert.Equal(t, tt.wantUnwrap, tt.err.Unwrap())
		})
	}
}

func TestNewConfigWithMultipleOptions(t *testing.T) {
	config, err := NewConfig(
		WithMaxDepth(15),
		WithMinSamplesSplit(10),
		WithMinSamplesLeaf(5),
		WithCriterion("entropy"),
		WithVerbose(true),
	)

	require.NoError(t, err)
	assert.Equal(t, 15, config.MaxDepth)
	assert.Equal(t, 10, config.MinSamplesSplit)
	assert.Equal(t, 5, config.MinSamplesLeaf)
	assert.Equal(t, "entropy", config.Criterion)
	assert.True(t, config.Verbose)
}

func TestWithCriterionCaseInsensitive(t *testing.T) {
	tests := []struct {
		name      string
		criterion string
		want      string
		wantErr   bool
	}{
		{name: "uppercase", criterion: "GINI", want: "gini", wantErr: false},
		{name: "mixed case", criterion: "EnTrOpY", want: "entropy", wantErr: false},
		{name: "with spaces", criterion: " mse ", want: "mse", wantErr: false},
		{name: "invalid", criterion: "invalid", want: "", wantErr: true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &Config{}
			err := WithCriterion(tt.criterion)(config)

			if (err != nil) != tt.wantErr {
				t.Errorf("WithCriterion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				assert.Equal(t, tt.want, config.Criterion)
			}
		})
	}
}

func TestParseFlags_HelpAndVersionHandling(t *testing.T) {
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	// Test help flag (this will exit, so we can't easily test it in unit tests)
	// Instead, we'll test the individual flag parsing without the exit behavior

	tests := []struct {
		name string
		args []string
		// These tests are tricky because --help and --version cause os.Exit()
		// We'll focus on other edge cases
	}{
		// We'll test other scenarios that don't cause exits
	}

	// Test flag parsing with various combinations
	tmpFile, err := os.CreateTemp("", "test*.csv")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	// Test with all flags
	os.Args = []string{"prog", "-c", "train", "-i", tmpFile.Name(), "-t", "target", "-o", "model.dt",
		"--max-depth", "5", "--min-samples", "10", "--min-leaf", "3", "--criterion", "entropy", "--verbose"}
	flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ContinueOnError)

	config, err := ParseFlags()
	require.NoError(t, err)

	assert.Equal(t, "train", config.Command)
	assert.Equal(t, tmpFile.Name(), config.InputFile)
	assert.Equal(t, "target", config.TargetCol)
	assert.Equal(t, "model.dt", config.OutputFile)
	assert.Equal(t, 5, config.MaxDepth)
	assert.Equal(t, 10, config.MinSamplesSplit)
	assert.Equal(t, 3, config.MinSamplesLeaf)
	assert.Equal(t, "entropy", config.Criterion)
	assert.True(t, config.Verbose)

	_ = tests // Avoid unused variable
}

func TestValidateDecisionTreeParamsEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		wantErr     bool
		errContains string
	}{
		{
			name: "negative max depth",
			config: &Config{
				MaxDepth:        -5,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
			wantErr:     true,
			errContains: "max depth must be positive",
		},
		{
			name: "zero min samples leaf",
			config: &Config{
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  0,
				Criterion:       "entropy",
			},
			wantErr:     true,
			errContains: "min samples leaf must be >= 1",
		},
		{
			name: "empty criterion",
			config: &Config{
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "",
			},
			wantErr:     true,
			errContains: "invalid criterion",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.validateDecisionTreeParams()

			if (err != nil) != tt.wantErr {
				t.Errorf("validateDecisionTreeParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && tt.errContains != "" {
				assert.Contains(t, err.Error(), tt.errContains)
			}
		})
	}
}
