MULBERRI IMPLEMENTATION COMPARISON REPORT
============================================================

EXECUTIVE SUMMARY
------------------------------
Total datasets compared: 8
Average training time difference: 0.200s
Average tree complexity difference: 120.6 nodes
Performance winner: main
Complexity winner: main (simpler)
Main-v2 faster on: 3 datasets
Main-v2 simpler on: 1 datasets

DETAILED DATASET COMPARISON
----------------------------------------

Dataset: bank
  Main-v2: 111 nodes, 0.201s
  Main:    52 nodes, 0.190s
  Difference: +59 nodes, +0.011s
  Complexity: main-v2 more complex

Dataset: credit_card
  Main-v2: 179 nodes, 1.293s
  Main:    116 nodes, 1.630s
  Difference: +63 nodes, -0.337s
  Complexity: main-v2 more complex

Dataset: home_loan
  Main-v2: 89 nodes, 0.067s
  Main:    33 nodes, 0.046s
  Difference: +56 nodes, +0.021s
  Complexity: main-v2 more complex

Dataset: hotel
  Main-v2: 397 nodes, 1.207s
  Main:    79 nodes, 0.959s
  Difference: +318 nodes, +0.248s
  Complexity: main-v2 more complex

Dataset: job_placement
  Main-v2: 17 nodes, 0.025s
  Main:    31 nodes, 0.038s
  Difference: -14 nodes, -0.013s
  Complexity: main more complex

Dataset: maintenance
  Main-v2: 83 nodes, 0.134s
  Main:    27 nodes, 0.240s
  Difference: +56 nodes, -0.106s
  Complexity: main-v2 more complex

Dataset: student
  Main-v2: 247 nodes, 0.508s
  Main:    75 nodes, 0.263s
  Difference: +172 nodes, +0.246s
  Complexity: main-v2 more complex

Dataset: telecom
  Main-v2: 319 nodes, 2.123s
  Main:    64 nodes, 0.592s
  Difference: +255 nodes, +1.531s
  Complexity: main-v2 more complex
