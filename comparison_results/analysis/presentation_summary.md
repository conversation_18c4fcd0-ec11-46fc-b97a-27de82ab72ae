# Mulberri Implementation Comparison
## Performance Benchmark Results

### 🎯 Key Results

- **Winner**: main branch (overall performance)
- **Datasets Tested**: 8
- **Average Speed Advantage**: 0.200s per dataset
- **Tree Complexity**: Main generates 121 fewer nodes on average

### 📊 Performance Breakdown

| Dataset | Main-v2 Time | Main Time | Winner | Complexity Winner |
|---------|--------------|-----------|--------|-------------------|
| bank | 0.201s | 0.190s | Main | Main |
| credit_card | 1.293s | 1.630s | Main-v2 | Main |
| home_loan | 0.067s | 0.046s | Main | Main |
| hotel | 1.207s | 0.959s | Main | Main |
| job_placement | 0.025s | 0.038s | Main-v2 | Main-v2 |
| maintenance | 0.134s | 0.240s | Main-v2 | Main |
| student | 0.508s | 0.263s | Main | Main |
| telecom | 2.123s | 0.592s | Main | Main |

### 🏆 Recommendations

**For Production**: Use **main** branch
- ✅ Faster training (5 out of 8 datasets)
- ✅ Simpler trees (better interpretability)
- ✅ More stable implementation

**For main-v2**: Needs optimization
- ⚠️ Slower on most datasets
- ⚠️ Generates overly complex trees
- 🔧 Requires pruning improvements
