#!/usr/bin/env python3
"""
Convert benchmark metadata files to main-v2 format.

The main-v2 format requires a 'handle_as' field that specifies how to process the data internally:
- numeric -> handle_as: float (or integer for discrete numeric)
- nominal -> handle_as: string
- binary -> handle_as: string
"""

import yaml
import os
import glob
from typing import Dict, Any


def convert_metadata_to_mainv2_format(old_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Convert old metadata format to main-v2 format with handle_as field."""
    new_metadata = {}
    
    for feature_name, feature_info in old_metadata.items():
        feature_type = feature_info.get('type', 'nominal')
        
        # Create new feature info with handle_as field
        new_feature_info = {
            'type': feature_type
        }
        
        # Determine handle_as based on type
        if feature_type == 'numeric':
            # For numeric features, use float as handle_as
            new_feature_info['handle_as'] = 'float'
            # Preserve min/max if they exist
            if 'min' in feature_info:
                new_feature_info['min'] = feature_info['min']
            if 'max' in feature_info:
                new_feature_info['max'] = feature_info['max']
        elif feature_type in ['nominal', 'binary']:
            # For nominal and binary features, use string as handle_as
            new_feature_info['handle_as'] = 'string'
            # Preserve values if they exist
            if 'values' in feature_info:
                new_feature_info['values'] = feature_info['values']
        else:
            # Default to string for unknown types
            new_feature_info['handle_as'] = 'string'
            # Preserve any additional fields
            for key, value in feature_info.items():
                if key not in ['type']:
                    new_feature_info[key] = value
        
        new_metadata[feature_name] = new_feature_info
    
    return new_metadata


def convert_metadata_file(input_path: str, output_path: str) -> None:
    """Convert a single metadata file from old format to main-v2 format."""
    print(f"Converting {input_path} -> {output_path}")
    
    # Load old metadata
    with open(input_path, 'r') as f:
        old_metadata = yaml.safe_load(f)
    
    # Convert to new format
    new_metadata = convert_metadata_to_mainv2_format(old_metadata)
    
    # Save new metadata
    with open(output_path, 'w') as f:
        yaml.dump(new_metadata, f, default_flow_style=False, sort_keys=False)


def main():
    """Convert all benchmark metadata files to main-v2 format."""
    benchmark_data_dir = "benchmark/data"
    output_dir = "benchmark/data_mainv2"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all metadata files
    metadata_files = glob.glob(os.path.join(benchmark_data_dir, "*_metadata.yaml"))
    
    print(f"Found {len(metadata_files)} metadata files to convert:")
    for metadata_file in metadata_files:
        print(f"  - {metadata_file}")
    
    # Convert each metadata file
    for metadata_file in metadata_files:
        # Extract dataset name from filename
        filename = os.path.basename(metadata_file)
        output_file = os.path.join(output_dir, filename)
        
        convert_metadata_file(metadata_file, output_file)
    
    print(f"\nConversion complete! New metadata files saved in {output_dir}/")
    
    # Also copy CSV files for convenience
    print("Copying CSV files for convenience...")
    import shutil
    csv_files = glob.glob(os.path.join(benchmark_data_dir, "*.csv"))
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        output_file = os.path.join(output_dir, filename)
        shutil.copy2(csv_file, output_file)
    
    print(f"Copied {len(csv_files)} CSV files to {output_dir}/")


if __name__ == "__main__":
    main()
