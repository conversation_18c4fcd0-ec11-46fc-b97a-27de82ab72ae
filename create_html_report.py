#!/usr/bin/env python3
"""
Create an HTML report with embedded visualizations.
"""

import json
import base64
import os

def load_comparison_data():
    """Load the comparison data."""
    with open('comparison_results/analysis/detailed_comparison.json', 'r') as f:
        return json.load(f)

def image_to_base64(image_path):
    """Convert image to base64 for embedding in HTML."""
    if os.path.exists(image_path):
        with open(image_path, 'rb') as f:
            return base64.b64encode(f.read()).decode()
    return None

def create_html_report():
    """Create comprehensive HTML report."""
    data = load_comparison_data()
    summary = data['summary']
    
    # Convert images to base64
    viz_dir = "comparison_results/visualizations"
    dashboard_b64 = image_to_base64(f"{viz_dir}/summary_dashboard.png")
    performance_b64 = image_to_base64(f"{viz_dir}/performance_comparison.png")
    complexity_b64 = image_to_base64(f"{viz_dir}/complexity_comparison.png")
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mulberri Implementation Comparison Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }}
        .summary-box {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }}
        .metric {{
            display: inline-block;
            margin: 10px 20px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            display: block;
        }}
        .metric-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .winner {{
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }}
        .chart {{
            text-align: center;
            margin: 30px 0;
        }}
        .chart img {{
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .recommendation {{
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
        }}
        .warning {{
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Mulberri Implementation Comparison Report</h1>
        
        <div class="summary-box">
            <h2 style="color: white; border: none; padding: 0;">Executive Summary</h2>
            <div class="metric">
                <span class="metric-value">{summary['total_datasets']}</span>
                <span class="metric-label">Datasets Tested</span>
            </div>
            <div class="metric">
                <span class="metric-value">{abs(summary['avg_training_time_diff']):.3f}s</span>
                <span class="metric-label">Avg Speed Advantage</span>
            </div>
            <div class="metric">
                <span class="metric-value">{abs(summary['avg_nodes_diff']):.0f}</span>
                <span class="metric-label">Avg Node Difference</span>
            </div>
            <div class="metric">
                <span class="winner">main branch WINS</span>
                <span class="metric-label">Overall Performance</span>
            </div>
        </div>

        <h2>📊 Comprehensive Dashboard</h2>
        <div class="chart">
            <img src="data:image/png;base64,{dashboard_b64}" alt="Summary Dashboard">
        </div>

        <h2>⚡ Training Performance Comparison</h2>
        <div class="chart">
            <img src="data:image/png;base64,{performance_b64}" alt="Performance Comparison">
        </div>

        <h2>🌳 Tree Complexity Comparison</h2>
        <div class="chart">
            <img src="data:image/png;base64,{complexity_b64}" alt="Complexity Comparison">
        </div>

        <h2>📋 Detailed Results</h2>
        <table>
            <thead>
                <tr>
                    <th>Dataset</th>
                    <th>Main-v2 Time</th>
                    <th>Main Time</th>
                    <th>Main-v2 Nodes</th>
                    <th>Main Nodes</th>
                    <th>Speed Winner</th>
                    <th>Complexity Winner</th>
                </tr>
            </thead>
            <tbody>
    """
    
    # Add dataset rows
    for dataset, d_data in data['datasets'].items():
        mv2_time = d_data['main_v2']['training_time']
        main_time = d_data['main']['training_time']
        mv2_nodes = d_data['main_v2']['total_nodes']
        main_nodes = d_data['main']['total_nodes']
        
        speed_winner = "main-v2" if mv2_time < main_time else "main"
        complexity_winner = "main-v2" if mv2_nodes < main_nodes else "main"
        
        html_content += f"""
                <tr>
                    <td><strong>{dataset}</strong></td>
                    <td>{mv2_time:.3f}s</td>
                    <td>{main_time:.3f}s</td>
                    <td>{mv2_nodes}</td>
                    <td>{main_nodes}</td>
                    <td>{speed_winner}</td>
                    <td>{complexity_winner}</td>
                </tr>
        """
    
    html_content += f"""
            </tbody>
        </table>

        <h2>🏆 Recommendations</h2>
        
        <div class="recommendation">
            <h3>✅ For Production: Use main branch</h3>
            <ul>
                <li>Faster training on {summary['total_datasets'] - summary['main_v2_faster_count']} out of {summary['total_datasets']} datasets</li>
                <li>Generates simpler trees (better interpretability)</li>
                <li>More stable, proven implementation</li>
                <li>Lower risk of overfitting</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ For main-v2: Needs Optimization</h3>
            <ul>
                <li>Slower on most datasets</li>
                <li>Generates overly complex trees</li>
                <li>Potential overfitting issues</li>
                <li>Requires performance tuning</li>
            </ul>
        </div>

        <h2>📁 Files Generated</h2>
        <ul>
            <li><strong>mulberri_comparison_report.pdf</strong> - Complete PDF report</li>
            <li><strong>summary_dashboard.png</strong> - Comprehensive dashboard</li>
            <li><strong>performance_comparison.png</strong> - Training time comparison</li>
            <li><strong>complexity_comparison.png</strong> - Tree complexity comparison</li>
            <li><strong>FINAL_REPORT.md</strong> - Executive summary</li>
            <li><strong>detailed_analysis.md</strong> - Technical analysis</li>
        </ul>

        <hr style="margin: 30px 0;">
        <p style="text-align: center; color: #7f8c8d;">
            <em>Report generated on September 4, 2025 | Analysis completed in ~30 minutes</em>
        </p>
    </div>
</body>
</html>
    """
    
    # Save HTML report
    html_file = "comparison_results/mulberri_comparison_report.html"
    with open(html_file, 'w') as f:
        f.write(html_content)
    
    print(f"HTML report created: {html_file}")
    return html_file

def main():
    """Generate HTML report."""
    print("Creating HTML report with embedded visualizations...")
    html_file = create_html_report()
    
    print("\n" + "="*60)
    print("HTML REPORT COMPLETE! 🌐")
    print("="*60)
    print(f"Open in browser: {html_file}")
    print("This report includes all visualizations and can be shared easily!")

if __name__ == "__main__":
    main()
