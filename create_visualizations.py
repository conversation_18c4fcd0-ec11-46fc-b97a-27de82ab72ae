#!/usr/bin/env python3
"""
Create beautiful visualizations for Mulberri implementation comparison.

This script generates professional charts and graphs comparing main-v2 vs main implementations,
and exports them as high-quality images and PDF.
"""

import json
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
from matplotlib.backends.backend_pdf import PdfPages

# Set style for professional-looking plots
plt.style.use('default')
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3

def load_comparison_data():
    """Load the comparison data."""
    with open('comparison_results/analysis/detailed_comparison.json', 'r') as f:
        return json.load(f)

def create_performance_comparison(data, save_path):
    """Create training time comparison chart."""
    datasets = list(data['datasets'].keys())
    main_v2_times = [data['datasets'][d]['main_v2']['training_time'] for d in datasets]
    main_times = [data['datasets'][d]['main']['training_time'] for d in datasets]
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    x = np.arange(len(datasets))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, main_v2_times, width, label='main-v2', color='#ff7f7f', alpha=0.8)
    bars2 = ax.bar(x + width/2, main_times, width, label='main', color='#7fbf7f', alpha=0.8)
    
    ax.set_xlabel('Datasets', fontsize=12, fontweight='bold')
    ax.set_ylabel('Training Time (seconds)', fontsize=12, fontweight='bold')
    ax.set_title('Training Time Comparison: main-v2 vs main', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(datasets, rotation=45, ha='right')
    ax.legend(fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax.annotate(f'{height:.3f}s',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        ax.annotate(f'{height:.3f}s',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    return fig

def create_complexity_comparison(data, save_path):
    """Create tree complexity comparison chart."""
    datasets = list(data['datasets'].keys())
    main_v2_nodes = [data['datasets'][d]['main_v2']['total_nodes'] for d in datasets]
    main_nodes = [data['datasets'][d]['main']['total_nodes'] for d in datasets]
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    x = np.arange(len(datasets))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, main_v2_nodes, width, label='main-v2', color='#ff9999', alpha=0.8)
    bars2 = ax.bar(x + width/2, main_nodes, width, label='main', color='#99ff99', alpha=0.8)
    
    ax.set_xlabel('Datasets', fontsize=12, fontweight='bold')
    ax.set_ylabel('Total Nodes', fontsize=12, fontweight='bold')
    ax.set_title('Tree Complexity Comparison: main-v2 vs main', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(datasets, rotation=45, ha='right')
    ax.legend(fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax.annotate(f'{int(height)}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        ax.annotate(f'{int(height)}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    return fig

def create_summary_dashboard(data, save_path):
    """Create a comprehensive dashboard."""
    fig = plt.figure(figsize=(16, 12))
    
    # Summary statistics
    summary = data['summary']
    datasets = data['datasets']
    
    # Create subplots
    gs = fig.add_gridspec(3, 2, height_ratios=[1, 2, 2], hspace=0.3, wspace=0.3)
    
    # Top: Summary metrics
    ax_summary = fig.add_subplot(gs[0, :])
    ax_summary.axis('off')
    
    summary_text = f"""
    MULBERRI IMPLEMENTATION COMPARISON SUMMARY
    
    Total Datasets: {summary['total_datasets']}    |    Performance Winner: {summary['performance_winner']}    |    Complexity Winner: {summary['complexity_winner']}
    
    Average Time Difference: {summary['avg_training_time_diff']:.3f}s    |    Average Node Difference: {summary['avg_nodes_diff']:.0f} nodes
    """
    
    ax_summary.text(0.5, 0.5, summary_text, ha='center', va='center', fontsize=14, 
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    # Middle left: Performance scatter plot
    ax_perf = fig.add_subplot(gs[1, 0])
    
    dataset_names = list(datasets.keys())
    main_v2_times = [datasets[d]['main_v2']['training_time'] for d in dataset_names]
    main_times = [datasets[d]['main']['training_time'] for d in dataset_names]
    
    ax_perf.scatter(main_times, main_v2_times, s=100, alpha=0.7, c='red')
    
    # Add diagonal line (equal performance)
    max_time = max(max(main_v2_times), max(main_times))
    ax_perf.plot([0, max_time], [0, max_time], 'k--', alpha=0.5, label='Equal Performance')
    
    ax_perf.set_xlabel('main Training Time (s)', fontweight='bold')
    ax_perf.set_ylabel('main-v2 Training Time (s)', fontweight='bold')
    ax_perf.set_title('Performance Scatter Plot', fontweight='bold')
    ax_perf.grid(True, alpha=0.3)
    ax_perf.legend()
    
    # Add dataset labels
    for i, name in enumerate(dataset_names):
        ax_perf.annotate(name, (main_times[i], main_v2_times[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # Middle right: Complexity scatter plot
    ax_comp = fig.add_subplot(gs[1, 1])
    
    main_v2_nodes = [datasets[d]['main_v2']['total_nodes'] for d in dataset_names]
    main_nodes = [datasets[d]['main']['total_nodes'] for d in dataset_names]
    
    ax_comp.scatter(main_nodes, main_v2_nodes, s=100, alpha=0.7, c='blue')
    
    # Add diagonal line
    max_nodes = max(max(main_v2_nodes), max(main_nodes))
    ax_comp.plot([0, max_nodes], [0, max_nodes], 'k--', alpha=0.5, label='Equal Complexity')
    
    ax_comp.set_xlabel('main Total Nodes', fontweight='bold')
    ax_comp.set_ylabel('main-v2 Total Nodes', fontweight='bold')
    ax_comp.set_title('Complexity Scatter Plot', fontweight='bold')
    ax_comp.grid(True, alpha=0.3)
    ax_comp.legend()
    
    # Add dataset labels
    for i, name in enumerate(dataset_names):
        ax_comp.annotate(name, (main_nodes[i], main_v2_nodes[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # Bottom: Win/Loss summary
    ax_wins = fig.add_subplot(gs[2, :])
    
    # Calculate wins for each metric
    time_wins = {'main-v2': 0, 'main': 0}
    complexity_wins = {'main-v2': 0, 'main': 0}
    
    for dataset, d_data in datasets.items():
        if d_data['differences']['training_time_diff'] < 0:
            time_wins['main-v2'] += 1
        else:
            time_wins['main'] += 1
            
        if d_data['differences']['nodes_diff'] < 0:
            complexity_wins['main-v2'] += 1
        else:
            complexity_wins['main'] += 1
    
    categories = ['Training Speed', 'Tree Simplicity']
    main_v2_wins = [time_wins['main-v2'], complexity_wins['main-v2']]
    main_wins = [time_wins['main'], complexity_wins['main']]
    
    x = np.arange(len(categories))
    width = 0.35
    
    bars1 = ax_wins.bar(x - width/2, main_v2_wins, width, label='main-v2 wins', color='#ff7f7f')
    bars2 = ax_wins.bar(x + width/2, main_wins, width, label='main wins', color='#7fbf7f')
    
    ax_wins.set_xlabel('Metrics', fontweight='bold')
    ax_wins.set_ylabel('Number of Dataset Wins', fontweight='bold')
    ax_wins.set_title('Win/Loss Summary (out of 8 datasets)', fontweight='bold')
    ax_wins.set_xticks(x)
    ax_wins.set_xticklabels(categories)
    ax_wins.legend()
    ax_wins.grid(True, alpha=0.3)
    
    # Add value labels
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax_wins.annotate(f'{int(height)}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),
                            textcoords="offset points",
                            ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    plt.suptitle('Mulberri Implementation Comparison Dashboard', fontsize=20, fontweight='bold', y=0.98)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    return fig

def main():
    """Generate all visualizations."""
    print("Loading comparison data...")
    data = load_comparison_data()
    
    print("Creating visualizations...")
    
    # Create output directory
    import os
    viz_dir = "comparison_results/visualizations"
    os.makedirs(viz_dir, exist_ok=True)
    
    # Generate individual charts
    print("1. Creating performance comparison chart...")
    fig1 = create_performance_comparison(data, f"{viz_dir}/performance_comparison.png")
    
    print("2. Creating complexity comparison chart...")
    fig2 = create_complexity_comparison(data, f"{viz_dir}/complexity_comparison.png")
    
    print("3. Creating comprehensive dashboard...")
    fig3 = create_summary_dashboard(data, f"{viz_dir}/summary_dashboard.png")
    
    # Create PDF with all charts
    print("4. Creating PDF report...")
    with PdfPages(f"{viz_dir}/mulberri_comparison_report.pdf") as pdf:
        pdf.savefig(fig3, bbox_inches='tight')
        pdf.savefig(fig1, bbox_inches='tight')
        pdf.savefig(fig2, bbox_inches='tight')
    
    plt.close('all')
    
    print("\n" + "="*60)
    print("VISUALIZATIONS COMPLETE! 🎉")
    print("="*60)
    print(f"Files created in: {viz_dir}/")
    print("- performance_comparison.png")
    print("- complexity_comparison.png") 
    print("- summary_dashboard.png")
    print("- mulberri_comparison_report.pdf")
    print("\nPerfect for your presentation! 📊")

if __name__ == "__main__":
    main()
