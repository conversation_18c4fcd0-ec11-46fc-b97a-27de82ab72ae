package cli

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/config"
	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/io/persistence"
	"github.com/berrijam/mulberri/internal/training"
	"github.com/berrijam/mulberri/internal/utils/logger"
	"github.com/spf13/cobra"
)

// NewTrainCommand creates the train subcommand for building decision trees.
//
// Handles training-specific flags, validation, and delegates to training package.
// Uses defaults from config package and validates all required parameters.
//
// Returns cobra command configured with training flags and validation.
func NewTrainCommand() *cobra.Command {
	cfg := &TrainingConfig{
		MaxDepth:        config.DefaultMaxDepth,
		MinSamplesSplit: config.DefaultMinSamples,
		Criterion:       config.DefaultCriterion,
	}

	trainCmd := &cobra.Command{
		Use:   "train",
		Short: "Train a C4.5 decision tree model",
		Long: `Train a decision tree model from CSV data using the C4.5 algorithm.

The training process builds an interpretable tree structure optimized for
classification tasks with configurable depth and splitting criteria.`,
		Example: `  # Basic training
  mulberri train -i data.csv -t species -o model.dt -f features.yaml

  # Training with custom parameters
  mulberri train -i data.csv -t target -o model.dt -f features.yaml --max-depth 15 --verbose

  # Training with different feature specification
  mulberri train -i data.csv -t target -o model.dt -f custom_features.yaml`,
		RunE: func(cmd *cobra.Command, args []string) error {
			CurrentSubcommand = cmd.Name()

			// Set up logger configuration with verbose flag from CLI
			logConfig := logger.LogConfig{
				LogFolder:     "logs",
				MaxSize:       10,
				EnableConsole: true,
				AppName:       "mulberri",
				EnableColors:  true,
				MaxBackups:    7,
				MaxAge:        7,
				Verbose:       cfg.Verbose,
				Operation:     "train",
			}

			// Initialize logger with the configuration
			if err := logger.SetGlobalConfig(logConfig); err != nil {
				fmt.Printf("Failed to initialize logger: %v\n", err)
				return err
			}

			cfg.Validate()
			runTraining(cfg)
			return nil
		},
	}

	// Required flags
	trainCmd.Flags().StringVarP(&cfg.InputFile, "input", "i", "", "Input CSV file path")
	trainCmd.Flags().StringVarP(&cfg.TargetCol, "target", "t", "", "Target column name")
	trainCmd.Flags().StringVarP(&cfg.OutputFile, "output", "o", "", "Output model file path")
	trainCmd.Flags().StringVarP(&cfg.FeatureInfoFile, "features", "f", "", "Feature info YAML file path")

	// Optional flags with defaults
	trainCmd.Flags().IntVar(&cfg.MaxDepth, "max-depth", cfg.MaxDepth, "Maximum tree depth")
	trainCmd.Flags().IntVar(&cfg.MinSamplesSplit, "min-samples", cfg.MinSamplesSplit, "Minimum samples to split")
	trainCmd.Flags().StringVar(&cfg.Criterion, "criterion", cfg.Criterion, "Split criterion (entropy only)")
	trainCmd.Flags().BoolVar(&cfg.BinarySplitsOnly, "binary-splits-only", true, "Force binary splits for categorical features (default: true)")
	trainCmd.Flags().BoolVarP(&cfg.Verbose, "verbose", "v", false, "Enable verbose output")

	// Mark required flags
	trainCmd.MarkFlagRequired("input")
	trainCmd.MarkFlagRequired("target")
	trainCmd.MarkFlagRequired("output")
	trainCmd.MarkFlagRequired("features")

	return trainCmd
}

// runTraining executes the training workflow with validated configuration.
//
// Args:
// - cfg: Validated training configuration
//
// Workflow:
// 1. Load feature information from YAML file
// 2. Load and validate CSV data
// 3. Validate CSV columns against feature info
// 4. Log dataset statistics
// 5. TODO: Build decision tree model
// 6. TODO: Save trained model
//
// Side effects:
// - Creates log entries for training progress
// - Exits if data loading fails
// - May create output files in future iterations
func runTraining(cfg *TrainingConfig) {
	logger.Info("Starting C4.5 decision tree training")
	logger.Info(fmt.Sprintf("Input: %s, Target: %s, Output: %s",
		cfg.InputFile, cfg.TargetCol, cfg.OutputFile))

	logger.Info(fmt.Sprintf("Training parameters: max_depth=%d, min_samples=%d, criterion=%s",
		cfg.MaxDepth, cfg.MinSamplesSplit, cfg.Criterion))

	// Step 1: Load feature information from YAML file
	logger.Info("Loading feature information from YAML file")
	featureLoader := features.NewFeatureLoader()
	featureConfig := featureLoader.LoadFeatureInfo(cfg.FeatureInfoFile)

	logger.Info(fmt.Sprintf("Loaded feature info for %d features", len(*featureConfig)))

	// Step 2: Load CSV data
	logger.Info("Loading CSV training data")
	datasetLoader := dataset.NewLoader()
	csvData := datasetLoader.LoadCSV(cfg.InputFile)

	if csvData == nil {
		logger.Fatal("Failed to load CSV data")
		return
	}

	logger.Info(fmt.Sprintf("Loaded CSV data: %d rows, %d columns",
		csvData.NumRows, csvData.NumColumns))

	logger.Debug(fmt.Sprintf("CSV headers: %v", csvData.Headers))

	// Step 3: Validate CSV columns against feature information
	logger.Info("Validating CSV Column names against feature information")
	features.ValidateCSVColumnsAgainstFeatureInfo(featureConfig, csvData.Headers, cfg.TargetCol)
	logger.Info("CSV validation completed successfully")

	// Step 4: Target column validation will be handled by LoadCSVToDataset

	// Step 5: Memory cleanup for CSV data (safety net - explicit release happens after dataset creation)
	defer func() {
		if csvData != nil {
			csvData.Release()
		}
	}()

	// Step 6: Convert YAML feature configuration to FeatureType map
	logger.Info("Converting feature configuration to internal types")
	allFeatureTypes := features.ConvertToFeatureTypeMap(featureConfig)

	// Exclude target column from features used for splitting
	featureTypes := make(map[string]features.FeatureType)
	for featureName, featureType := range allFeatureTypes {
		if featureName != cfg.TargetCol {
			featureTypes[featureName] = featureType
		}
	}

	logger.Info(fmt.Sprintf("Converted %d features to internal types (%d for splitting, target '%s' excluded)",
		len(allFeatureTypes), len(featureTypes), cfg.TargetCol))

	// Step 7: Convert CSV data to typed Dataset with feature info
	logger.Info("Converting CSV data to typed dataset")
	typedDataset := dataset.LoadCSVToDataset[string](cfg.InputFile, allFeatureTypes, cfg.TargetCol)
	if typedDataset == nil {
		logger.Fatal("Failed to convert CSV to typed dataset")
		return
	}

	// Explicitly release CSV data memory now that we have the typed dataset
	if csvData != nil {
		csvData.Release()
		csvData = nil // Set to nil to prevent double-release in defer
	}

	logger.Info(fmt.Sprintf("Successfully created typed dataset: %d rows, %d features",
		typedDataset.GetRowCount(), len(typedDataset.GetFeatureOrder())))

	// Step 8: Build decision tree using C4.5 algorithm
	logger.Info("Building decision tree using C4.5 algorithm")

	// Create tree builder with configuration
	buildConfig := createBuildConfig(cfg)
	treeBuilder, err := createTreeBuilder(buildConfig)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Failed to create tree builder: %v", err))
		return
	}

	// Create dataset view for tree building (use all data)
	allIndices := make([]int, typedDataset.GetRowCount())
	for i := range allIndices {
		allIndices[i] = i
	}
	trainingView := typedDataset.CreateView(allIndices)

	logger.Info(fmt.Sprintf("Starting tree construction with %d samples", trainingView.GetSize()))

	// Build the decision tree
	decisionTree, err := treeBuilder.BuildTree(trainingView, featureTypes, cfg.TargetCol)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Failed to build decision tree: %v", err))
		return
	}

	logger.Info("Decision tree construction completed successfully")

	// Step 9: Save trained model to output file
	logger.Info(fmt.Sprintf("Saving trained model to: %s", cfg.OutputFile))

	// Use persistence package for proper separation of concerns
	serializer := persistence.NewTreeSerializer()
	err = serializer.SaveTreeToFile(decisionTree, cfg.OutputFile)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Failed to save model: %v", err))
		return
	}

	logger.Info("Model saved successfully")

	// Final success message with comprehensive statistics
	logger.Info("Training completed successfully!")
	logger.Info(fmt.Sprintf("- Loaded %d training samples with %d features", typedDataset.GetRowCount(), len(typedDataset.GetFeatureOrder())))
	logger.Info(fmt.Sprintf("- Feature info validated and converted for %d features", len(featureTypes)))
	logger.Info(fmt.Sprintf("- Target column '%s' processed successfully", cfg.TargetCol))
	logger.Info(fmt.Sprintf("- Decision tree built: %d total nodes (%d leaves)",
		decisionTree.Metadata.TotalNodes, decisionTree.Metadata.LeafNodes))
	logger.Info(fmt.Sprintf("- Maximum tree depth: %d", decisionTree.Metadata.MaxDepth))
	logger.Info(fmt.Sprintf("- Model saved to: %s", cfg.OutputFile))
}

// createBuildConfig creates tree builder configuration from CLI training configuration.
//
// Maps CLI parameters to tree builder configuration, applying defaults and
// validation as needed for the C4.5 algorithm implementation.
//
// Args:
// - cfg: CLI training configuration with user-specified parameters
//
// Returns: BuildConfig suitable for TreeBuilder initialization.
// Constraints: cfg must be validated CLI configuration.
// Performance: O(1) configuration mapping.
// Relationships: Bridges CLI configuration to tree builder configuration.
// Side effects: None (pure configuration transformation).
//
// Example: Internal helper for runTraining workflow.
func createBuildConfig(cfg *TrainingConfig) training.BuildConfig {
	return training.BuildConfig{
		MaxDepth:         cfg.MaxDepth,
		MinSamplesSplit:  cfg.MinSamplesSplit,
		MinSamplesLeaf:   config.DefaultMinSamplesLeaf, // Default minimum samples per leaf
		Criterion:        cfg.Criterion,
		MinGainThreshold: config.DefaultMinEntropyGain, // Default minimum gain threshold
	}
}

// createTreeBuilder creates and validates a TreeBuilder instance.
//
// Initializes TreeBuilder with the provided configuration and validates
// that all parameters are suitable for tree construction.
//
// Args:
// - config: BuildConfig with tree construction parameters
//
// Returns: TreeBuilder instance or error if configuration invalid.
// Constraints: config must pass TreeBuilder validation.
// Performance: O(1) builder initialization.
// Relationships: Creates TreeBuilder for use in training workflow.
// Side effects: Logs builder configuration, allocates TreeBuilder.
//
// Example: Internal helper for runTraining workflow.
func createTreeBuilder(config training.BuildConfig) (*training.TreeBuilder, error) {
	treeBuilder, err := training.NewTreeBuilder(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create tree builder: %w", err)
	}

	logger.Debug("Tree builder created successfully with validated configuration")
	return treeBuilder, nil
}
