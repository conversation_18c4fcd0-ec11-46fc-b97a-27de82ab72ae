// Package persistence provides tree serialization and deserialization functionality.
//
// This package handles the conversion of decision trees to and from persistent storage
// formats, primarily JSON. It implements the I/O operations for trained models while
// keeping the tree data structures clean and focused on their core responsibilities.
//
// Design Principles:
// - Separation of concerns: I/O logic separate from tree logic
// - Format-agnostic interface for future format support
// - Complete round-trip serialization fidelity
// - Error handling with detailed context
//
// Architecture:
// - TreeSerializer: Main service for tree persistence operations
// - JSON format support with custom marshaling/unmarshaling
// - Integration with tree package for seamless API
//
// Example:
//
//	serializer := NewTreeSerializer()
//	err := serializer.SaveTreeToFile(tree, "model.json")
//	loadedTree, err := serializer.LoadTreeFromFile("model.json")
//
// Security: Validates file paths and content during load operations.
// Performance: Streaming JSON for large trees, efficient memory usage.
// Dependencies: tree package, standard JSON library, file system operations.
package persistence

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/berrijam/mulberri/internal/tree"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// TreeSerializer provides tree persistence operations.
//
// Handles serialization and deserialization of decision trees to various formats.
// Currently supports JSON format with plans for future format extensibility.
//
// Design Pattern: Service pattern with format-specific implementations
// - Encapsulates all persistence logic
// - Provides clean interface for tree package
// - Handles error cases and validation
// - Supports multiple output formats (future)
//
// Constraints:
// - File paths must be valid and accessible
// - Trees must be valid and complete for serialization
// - JSON format must be well-formed for deserialization
//
// Security: Validates file paths and content integrity.
// Performance: Efficient JSON streaming for large trees.
// Relationships: Used by tree.DecisionTree methods for persistence.
// Side effects: Creates/reads files, logs operations.
type TreeSerializer struct {
	// Future: format-specific serializers can be added here
}

// NewTreeSerializer creates a new TreeSerializer instance.
//
// Initializes the serializer with default configuration for tree persistence.
// Currently supports JSON format with potential for future format extensions.
//
// Returns: New TreeSerializer ready for use.
// Constraints: None (simple constructor).
// Performance: O(1) initialization.
// Relationships: Used by tree package for persistence operations.
// Side effects: None (pure constructor).
//
// Example: serializer := NewTreeSerializer()
func NewTreeSerializer() *TreeSerializer {
	return &TreeSerializer{}
}

// SaveTreeToFile serializes a decision tree to a JSON file.
//
// Converts the complete decision tree including metadata, features, and node
// structure to JSON format and writes to the specified file. Handles all
// serialization complexities including custom node marshaling.
//
// Args:
// - tree: DecisionTree to serialize (must be valid and complete)
// - filename: Target file path (will be created/overwritten)
//
// Returns: Error if serialization or file writing fails, nil on success.
// Constraints: tree must be valid, filename must be writable path.
// Performance: O(tree_size) for serialization, O(1) for file operations.
// Relationships: Used by tree.DecisionTree.SaveTreeToFile method.
// Side effects: Creates/overwrites file, logs operation.
//
// Algorithm:
// 1. Validate input parameters
// 2. Prepare tree for serialization (convert nodes to serializable format)
// 3. Marshal to JSON with indentation
// 4. Write to file atomically
// 5. Log success/failure
//
// Example:
//
//	err := serializer.SaveTreeToFile(tree, "model.json")
func (ts *TreeSerializer) SaveTreeToFile(tree *tree.DecisionTree, filename string) error {
	if tree == nil {
		return fmt.Errorf("cannot save nil tree")
	}
	if filename == "" {
		return fmt.Errorf("filename cannot be empty")
	}

	logger.Debug(fmt.Sprintf("Serializing tree to file: %s", filename))

	// Prepare tree for serialization by converting root to serializable format
	if err := ts.prepareTreeForSerialization(tree); err != nil {
		return fmt.Errorf("failed to prepare tree for serialization: %w", err)
	}

	// Marshal tree to JSON with indentation for readability
	data, err := json.MarshalIndent(tree, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal tree to JSON: %w", err)
	}

	// Write to file atomically
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write tree to file %s: %w", filename, err)
	}

	// Log success with tree statistics
	nodeCount := 0
	leafCount := 0
	if tree.Metadata != nil {
		nodeCount = tree.Metadata.TotalNodes
		leafCount = tree.Metadata.LeafNodes
	}

	logger.Info(fmt.Sprintf("tree saved to %s (%d nodes, %d leaves)", filename, nodeCount, leafCount))
	return nil
}

// LoadTreeFromFile deserializes a decision tree from a JSON file.
//
// Reads JSON data from file and reconstructs the complete decision tree
// including metadata, features, and node structure. Handles all deserialization
// complexities including node reconstruction from serializable format.
//
// Args:
// - filename: Source file path (must exist and be readable)
//
// Returns: Reconstructed DecisionTree or error if loading fails.
// Constraints: filename must exist, contain valid JSON tree format.
// Performance: O(tree_size) for deserialization, O(1) for file operations.
// Relationships: Used by tree package for model loading.
// Side effects: Reads file, logs operation.
//
// Algorithm:
// 1. Validate filename and check file existence
// 2. Read file content
// 3. Unmarshal JSON to tree structure
// 4. Reconstruct nodes from serializable format
// 5. Validate reconstructed tree
// 6. Log success/failure
//
// Example:
//
//	tree, err := serializer.LoadTreeFromFile("model.json")
func (ts *TreeSerializer) LoadTreeFromFile(filename string) (*tree.DecisionTree, error) {
	if filename == "" {
		return nil, fmt.Errorf("filename cannot be empty")
	}

	logger.Debug(fmt.Sprintf("Loading tree from file: %s", filename))

	// Read file content
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	// Unmarshal JSON to tree structure
	var loadedTree tree.DecisionTree
	if err := json.Unmarshal(data, &loadedTree); err != nil {
		return nil, fmt.Errorf("failed to unmarshal tree from JSON: %w", err)
	}

	// Reconstruct nodes from serializable format
	if err := ts.reconstructTreeFromSerialization(&loadedTree); err != nil {
		return nil, fmt.Errorf("failed to reconstruct tree from serialization: %w", err)
	}

	// Validate reconstructed tree
	if err := ts.validateReconstructedTree(&loadedTree); err != nil {
		return nil, fmt.Errorf("reconstructed tree validation failed: %w", err)
	}

	// Log success with tree statistics
	nodeCount := 0
	leafCount := 0
	if loadedTree.Metadata != nil {
		nodeCount = loadedTree.Metadata.TotalNodes
		leafCount = loadedTree.Metadata.LeafNodes
	}

	logger.Info(fmt.Sprintf("tree loaded from %s (%d nodes, %d leaves)", filename, nodeCount, leafCount))
	return &loadedTree, nil
}

// prepareTreeForSerialization converts tree nodes to serializable format.
//
// Transforms the tree's root node and its children into SerializableNode format
// that can be properly marshaled to JSON. This handles the conversion from
// the runtime Node interface to the JSON-compatible structure.
//
// Args:
// - tree: DecisionTree to prepare (must have valid root)
//
// Returns: Error if conversion fails, nil on success.
// Performance: O(tree_size) for complete tree traversal.
// Relationships: Helper for SaveTreeToFile.
// Side effects: Modifies tree.SerializableRoot field.
func (ts *TreeSerializer) prepareTreeForSerialization(tree *tree.DecisionTree) error {
	if tree.Root == nil {
		return fmt.Errorf("tree has no root node")
	}

	// Convert root node to serializable format
	tree.SerializableRoot = tree.Root.ToSerializable()
	if tree.SerializableRoot == nil {
		return fmt.Errorf("failed to convert root node to serializable format")
	}

	return nil
}

// reconstructTreeFromSerialization converts serializable nodes back to runtime nodes.
//
// Transforms the SerializableNode structure back into the runtime Node interface
// implementations. This reconstructs the tree's navigable structure from the
// JSON-compatible format.
//
// Args:
// - tree: DecisionTree with SerializableRoot to reconstruct
//
// Returns: Error if reconstruction fails, nil on success.
// Performance: O(tree_size) for complete tree reconstruction.
// Relationships: Helper for LoadTreeFromFile.
// Side effects: Sets tree.Root field from SerializableRoot.
func (ts *TreeSerializer) reconstructTreeFromSerialization(tree *tree.DecisionTree) error {
	if tree.SerializableRoot == nil {
		return fmt.Errorf("tree has no serializable root")
	}

	// Use the tree's own reconstruction method to avoid circular dependency
	if err := tree.ReconstructFromSerialization(); err != nil {
		return fmt.Errorf("failed to reconstruct tree from serialization: %w", err)
	}

	return nil
}

// validateReconstructedTree performs validation on a reconstructed tree.
//
// Ensures that the tree loaded from file is valid and complete, checking
// node structure, metadata consistency, and feature definitions.
//
// Args:
// - tree: DecisionTree to validate
//
// Returns: Error if validation fails, nil if tree is valid.
// Performance: O(tree_size) for complete tree validation.
// Relationships: Helper for LoadTreeFromFile.
// Side effects: None (pure validation).
func (ts *TreeSerializer) validateReconstructedTree(tree *tree.DecisionTree) error {
	if tree.Root == nil {
		return fmt.Errorf("reconstructed tree has no root node")
	}

	if !tree.Root.Validate() {
		return fmt.Errorf("reconstructed tree root validation failed")
	}

	if tree.Metadata == nil {
		return fmt.Errorf("reconstructed tree has no metadata")
	}

	if len(tree.Features) == 0 {
		return fmt.Errorf("reconstructed tree has no features")
	}

	if len(tree.Classes) == 0 {
		return fmt.Errorf("reconstructed tree has no target classes")
	}

	return nil
}
