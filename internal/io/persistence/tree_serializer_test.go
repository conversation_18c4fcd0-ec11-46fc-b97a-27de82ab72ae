package persistence

import (
	"os"
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/tree"
)

// TestTreeSerializer_BasicSaveLoad tests basic save and load functionality.
func TestTreeSerializer_BasicSaveLoad(t *testing.T) {
	// Create a simple test tree
	feature, err := tree.NewFeature("test_feature", features.IntegerFeature)
	if err != nil {
		t.Fatalf("Failed to create feature: %v", err)
	}

	// Create a simple leaf node
	classDist := map[interface{}]int{"yes": 10, "no": 5}
	leafNode, err := tree.NewLeafNode(classDist)
	if err != nil {
		t.Fatalf("Failed to create leaf node: %v", err)
	}

	// Create metadata
	metadata := tree.NewTreeMetadata("C4.5", 5, 10, "entropy", 15)
	if metadata == nil {
		t.Fatal("Failed to create metadata")
	}

	// Create decision tree
	features := []*tree.Feature{feature}
	classes := []string{"yes", "no"}
	originalTree := tree.NewDecisionTree(leafNode, features, classes, metadata)
	if originalTree == nil {
		t.Fatal("Failed to create decision tree")
	}

	// Create serializer
	serializer := NewTreeSerializer()

	// Test save
	filename := "test_tree_serializer.json"
	defer os.Remove(filename) // Clean up

	err = serializer.SaveTreeToFile(originalTree, filename)
	if err != nil {
		t.Fatalf("Failed to save tree: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		t.Fatal("Tree file was not created")
	}

	// Test load
	loadedTree, err := serializer.LoadTreeFromFile(filename)
	if err != nil {
		t.Fatalf("Failed to load tree: %v", err)
	}

	if loadedTree == nil {
		t.Fatal("Loaded tree is nil")
	}

	// Verify basic properties
	if loadedTree.Root == nil {
		t.Error("Loaded tree has no root")
	}

	if len(loadedTree.Features) != 1 {
		t.Errorf("Expected 1 feature, got %d", len(loadedTree.Features))
	}

	if len(loadedTree.Classes) != 2 {
		t.Errorf("Expected 2 classes, got %d", len(loadedTree.Classes))
	}

	if loadedTree.Metadata == nil {
		t.Error("Loaded tree has no metadata")
	} else {
		if loadedTree.Metadata.Algorithm != "C4.5" {
			t.Errorf("Expected algorithm 'C4.5', got %s", loadedTree.Metadata.Algorithm)
		}
	}
}

// TestTreeSerializer_InvalidInputs tests error handling for invalid inputs.
func TestTreeSerializer_InvalidInputs(t *testing.T) {
	serializer := NewTreeSerializer()

	// Test save with nil tree
	err := serializer.SaveTreeToFile(nil, "test.json")
	if err == nil {
		t.Error("Expected error for nil tree, but got none")
	}

	// Test save with empty filename
	feature, _ := tree.NewFeature("test", features.IntegerFeature)
	leafNode, _ := tree.NewLeafNode(map[interface{}]int{"yes": 1})
	metadata := tree.NewTreeMetadata("C4.5", 5, 10, "entropy", 1)
	testTree := tree.NewDecisionTree(leafNode, []*tree.Feature{feature}, []string{"yes"}, metadata)

	err = serializer.SaveTreeToFile(testTree, "")
	if err == nil {
		t.Error("Expected error for empty filename, but got none")
	}

	// Test load with empty filename
	_, err = serializer.LoadTreeFromFile("")
	if err == nil {
		t.Error("Expected error for empty filename, but got none")
	}

	// Test load with non-existent file
	_, err = serializer.LoadTreeFromFile("non_existent_file.json")
	if err == nil {
		t.Error("Expected error for non-existent file, but got none")
	}
}

// TestTreeSerializer_RoundTrip tests that save/load preserves tree structure.
func TestTreeSerializer_RoundTrip(t *testing.T) {
	// Create a more complex tree with decision node and children
	ageFeature, err := tree.NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("Failed to create age feature: %v", err)
	}

	// Create decision node
	classDist := map[interface{}]int{"approved": 6, "rejected": 4}
	decisionNode, err := tree.NewDecisionNode(ageFeature, 30.0, classDist)
	if err != nil {
		t.Fatalf("Failed to create decision node: %v", err)
	}

	// Create left child (leaf)
	leftDist := map[interface{}]int{"rejected": 4}
	leftChild, err := tree.NewLeafNode(leftDist)
	if err != nil {
		t.Fatalf("Failed to create left child: %v", err)
	}

	// Create right child (leaf)
	rightDist := map[interface{}]int{"approved": 6}
	rightChild, err := tree.NewLeafNode(rightDist)
	if err != nil {
		t.Fatalf("Failed to create right child: %v", err)
	}

	// Set children
	decisionNode.SetChild(tree.LeftBranch, leftChild)
	decisionNode.SetChild(tree.RightBranch, rightChild)

	// Create tree
	features := []*tree.Feature{ageFeature}
	classes := []string{"approved", "rejected"}
	metadata := tree.NewTreeMetadata("C4.5", 10, 20, "entropy", 10)
	originalTree := tree.NewDecisionTree(decisionNode, features, classes, metadata)

	// Save and load
	serializer := NewTreeSerializer()
	filename := "test_roundtrip.json"
	defer os.Remove(filename)

	err = serializer.SaveTreeToFile(originalTree, filename)
	if err != nil {
		t.Fatalf("Failed to save tree: %v", err)
	}

	loadedTree, err := serializer.LoadTreeFromFile(filename)
	if err != nil {
		t.Fatalf("Failed to load tree: %v", err)
	}

	// Verify structure is preserved
	if loadedTree.Root.IsLeaf() {
		t.Error("Expected decision node as root, got leaf")
	}

	// Check that it's a decision node
	if decNode, ok := loadedTree.Root.(*tree.DecisionNode); ok {
		leftChild := decNode.GetChild(tree.LeftBranch)
		rightChild := decNode.GetChild(tree.RightBranch)

		if leftChild == nil {
			t.Error("Left child is missing")
		} else if !leftChild.IsLeaf() {
			t.Error("Left child should be a leaf")
		}

		if rightChild == nil {
			t.Error("Right child is missing")
		} else if !rightChild.IsLeaf() {
			t.Error("Right child should be a leaf")
		}
	} else {
		t.Error("Root is not a DecisionNode")
	}

	t.Logf("Round-trip test successful: %d nodes preserved", loadedTree.Metadata.TotalNodes)
}
