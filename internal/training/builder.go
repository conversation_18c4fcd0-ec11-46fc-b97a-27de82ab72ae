// Package training provides decision tree construction functionality for the C4.5 algorithm.
//
// This package implements the core recursive tree building algorithm that combines
// split evaluation, stopping criteria, and node construction to create complete
// decision trees from training data.
//
// Design Principles:
// - Recursive tree construction with configurable stopping criteria
// - Integration with existing split evaluation infrastructure
// - Memory-efficient view-based operations
// - Comprehensive tree statistics and metadata tracking
// - Support for both binary and n-ary tree structures (future)
//
// Architecture:
// - TreeBuilder: Main service for tree construction
// - BuildConfig: Configuration for tree building parameters
// - Integration with SplitEvaluator and DatasetView
//
// Example:
//
//	config := BuildConfig{
//	    MaxDepth:        10,
//	    MinSamplesSplit: 20,
//	    MinSamplesLeaf:  5,
//	    Criterion:       "entropy",
//	}
//	builder := NewTreeBuilder(config)
//	tree, err := builder.BuildTree(dataset, features, targetColumn)
//
// Security: No sensitive data storage, validates input parameters.
// Performance: O(n log n * f) where n=samples, f=features for tree construction.
// Dependencies: training package (splitters), tree package (nodes), dataset package (views).
package training

import (
	"fmt"
	"sort"

	"github.com/berrijam/mulberri/internal/config"
	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/tree"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// BuildConfig contains configuration parameters for tree construction.
//
// Defines stopping criteria, algorithm parameters, and tree structure constraints
// used during the recursive tree building process.
//
// Constraints:
// - MaxDepth must be > 0 (typically 5-20 for practical trees)
// - MinSamplesSplit must be >= 2 (need at least 2 samples to consider splitting)
// - MinSamplesLeaf must be >= 1 (leaves need at least 1 sample)
// - Criterion must be supported impurity measure ("entropy" for C4.5)
//
// Performance: Configuration is read-only during tree building.
// Relationships: Used by TreeBuilder for decision making during construction.
// Side effects: None (immutable configuration).
type BuildConfig struct {
	MaxDepth         int     // Maximum tree depth (0 = unlimited, practical limit ~20)
	MinSamplesSplit  int     // Minimum samples required to split a node
	MinSamplesLeaf   int     // Minimum samples required in a leaf node
	Criterion        string  // Split criterion ("entropy" for C4.5)
	MinGainThreshold float64 // Minimum information gain required for split (0.0 = no threshold)
}

// TreeBuilder implements the C4.5 decision tree construction algorithm.
//
// Provides recursive tree building with configurable stopping criteria and
// integration with the existing split evaluation infrastructure. Handles
// both the algorithmic logic and tree metadata management.
//
// Design Pattern: Builder pattern with configuration injection
// - Encapsulates tree construction algorithm
// - Uses dependency injection for split evaluators
// - Maintains tree statistics during construction
// - Supports different target types through generics
//
// Constraints:
// - Config must be valid (validated during construction)
// - Dataset must be non-empty with valid feature columns
// - Target column must exist and have valid values
//
// Security: No sensitive data storage, validates all inputs.
// Performance: O(n log n * f * d) where n=samples, f=features, d=depth.
// Relationships: Uses SplitEvaluator, DatasetView, and tree.Node interfaces.
// Side effects: Creates tree structure, logs construction progress.
type TreeBuilder struct {
	config       BuildConfig                    // Tree building configuration
	splitFactory *SplitEvaluatorFactory[string] // Factory for creating split evaluators
	treeStats    *TreeStatistics                // Statistics collected during building
}

// TreeStatistics tracks metrics collected during tree construction.
//
// Provides comprehensive statistics about the tree building process including
// node counts, depth information, and split quality metrics.
//
// Performance: Updated incrementally during tree construction.
// Relationships: Embedded in TreeBuilder, used for tree metadata.
// Side effects: Accumulates statistics as tree is built.
type TreeStatistics struct {
	TotalNodes    int     // Total number of nodes created
	LeafNodes     int     // Number of leaf nodes
	DecisionNodes int     // Number of decision nodes
	MaxDepth      int     // Actual maximum depth reached
	AvgDepth      float64 // Average depth of leaf nodes
	TotalSamples  int     // Total training samples processed
}

// NewTreeBuilder creates a new TreeBuilder with the specified configuration.
//
// Validates configuration parameters and initializes the builder with
// necessary dependencies for tree construction.
//
// Args:
// - config: Tree building configuration (must have valid parameters)
//
// Returns: New TreeBuilder instance or error if configuration invalid.
// Constraints: config must pass validation (positive values, supported criterion).
// Performance: O(1) initialization with configuration validation.
// Relationships: Creates SplitEvaluatorFactory for use during tree building.
// Side effects: Allocates TreeBuilder and dependencies, logs configuration.
//
// Example:
//
//	config := BuildConfig{MaxDepth: 10, MinSamplesSplit: 20, Criterion: "entropy"}
//	builder, err := NewTreeBuilder(config)
func NewTreeBuilder(config BuildConfig) (*TreeBuilder, error) {
	// Validate configuration parameters
	if err := validateBuildConfig(config); err != nil {
		logger.Error(fmt.Sprintf("Invalid tree builder configuration: %v", err))
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	logger.Info(fmt.Sprintf("Creating tree builder with config: max_depth=%d, min_samples_split=%d, min_samples_leaf=%d, criterion=%s",
		config.MaxDepth, config.MinSamplesSplit, config.MinSamplesLeaf, config.Criterion))

	return &TreeBuilder{
		config:       config,
		splitFactory: NewSplitEvaluatorFactory[string](),
		treeStats: &TreeStatistics{
			TotalNodes:    0,
			LeafNodes:     0,
			DecisionNodes: 0,
			MaxDepth:      0,
			AvgDepth:      0.0,
			TotalSamples:  0,
		},
	}, nil
}

// validateBuildConfig validates tree building configuration parameters.
//
// Ensures all configuration values are within acceptable ranges and
// that the specified criterion is supported.
//
// Args:
// - config: Configuration to validate
//
// Returns: Error if any parameter is invalid, nil if all valid.
// Constraints: Enforces business rules for tree construction parameters.
// Performance: O(1) parameter checking.
// Relationships: Used by NewTreeBuilder for input validation.
// Side effects: None (pure validation function).
func validateBuildConfig(config BuildConfig) error {
	if config.MaxDepth < 0 {
		return fmt.Errorf("max depth must be non-negative, got %d", config.MaxDepth)
	}
	if config.MinSamplesSplit < 2 {
		return fmt.Errorf("min samples split must be >= 2, got %d", config.MinSamplesSplit)
	}
	if config.MinSamplesLeaf < 1 {
		return fmt.Errorf("min samples leaf must be >= 1, got %d", config.MinSamplesLeaf)
	}
	if config.Criterion != "entropy" {
		return fmt.Errorf("unsupported criterion '%s', only 'entropy' is supported", config.Criterion)
	}
	if config.MinGainThreshold < 0.0 {
		return fmt.Errorf("min gain threshold must be non-negative, got %f", config.MinGainThreshold)
	}

	return nil
}

// BuildTree constructs a complete decision tree from the provided dataset.
//
// Implements the C4.5 algorithm using recursive tree construction with
// configurable stopping criteria. Creates a complete DecisionTree with
// root node, feature definitions, and comprehensive metadata.
//
// Args:
// - view: DatasetView containing training data (must be non-empty)
// - featureTypes: Map of feature names to their types (must match dataset columns)
// - targetColumn: Name of target column (must exist in dataset)
//
// Returns: Complete DecisionTree with trained root node and metadata.
// Constraints: view must be non-empty, all features must exist, target column must be valid.
// Performance: O(n log n * f * d) where n=samples, f=features, d=depth.
// Relationships: Creates tree.DecisionTree with tree.Node hierarchy.
// Side effects: Logs construction progress, updates tree statistics.
//
// Algorithm:
// 1. Initialize tree statistics and feature list
// 2. Build root node recursively using buildNode
// 3. Calculate final tree metadata and statistics
// 4. Create and return complete DecisionTree
//
// Example:
//
//	tree, err := builder.BuildTree(trainingView, featureTypes, "target")
func (tb *TreeBuilder) BuildTree(
	view *dataset.DatasetView[string],
	featureTypes map[string]features.FeatureType,
	targetColumn string,
) (*tree.DecisionTree, error) {
	if view == nil {
		return nil, fmt.Errorf("dataset view cannot be nil")
	}
	if view.GetSize() == 0 {
		return nil, fmt.Errorf("dataset view cannot be empty")
	}
	if len(featureTypes) == 0 {
		return nil, fmt.Errorf("feature types cannot be empty")
	}

	logger.Info(fmt.Sprintf("Starting tree construction with %d samples and %d features",
		view.GetSize(), len(featureTypes)))

	// Initialize tree statistics
	tb.treeStats.TotalSamples = view.GetSize()

	// Build feature list for tree metadata
	var treeFeatures []*tree.Feature
	for featureName, featureType := range featureTypes {
		feature, err := tree.NewFeature(featureName, featureType)
		if err != nil {
			return nil, fmt.Errorf("failed to create feature %s: %w", featureName, err)
		}
		treeFeatures = append(treeFeatures, feature)
	}

	// Sort features for consistent ordering
	sort.Slice(treeFeatures, func(i, j int) bool {
		return treeFeatures[i].Name < treeFeatures[j].Name
	})

	// Get unique target classes for tree metadata
	targetDist := view.GetTargetDistribution()
	var classes []string
	for class := range targetDist {
		classes = append(classes, class)
	}
	sort.Strings(classes) // Consistent ordering

	logger.Info(fmt.Sprintf("Target classes: %v, distribution: %v", classes, targetDist))

	// Build the tree recursively starting from root
	rootNode, err := tb.buildNode(view, featureTypes, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to build root node: %w", err)
	}

	// Calculate final tree statistics
	tb.calculateFinalStatistics(rootNode)

	// Create tree metadata
	metadata := tree.NewTreeMetadata(
		config.Algorithm,          // algorithm
		tb.config.MaxDepth,        // maxDepth
		tb.config.MinSamplesSplit, // minSamples
		tb.config.Criterion,       // criterion
		tb.treeStats.TotalSamples, // trainingSamples
	)

	// Create complete decision tree
	decisionTree := tree.NewDecisionTree(rootNode, treeFeatures, classes, metadata)
	if decisionTree == nil {
		return nil, fmt.Errorf("failed to create decision tree")
	}

	logger.Info(fmt.Sprintf("Tree construction completed: %d nodes (%d leaves, %d decisions), max depth %d",
		tb.treeStats.TotalNodes, tb.treeStats.LeafNodes, tb.treeStats.DecisionNodes, tb.treeStats.MaxDepth))

	return decisionTree, nil
}

// buildNode recursively constructs a single node (leaf or decision) from the given dataset view.
//
// Implements the core C4.5 algorithm logic including stopping criteria evaluation,
// best split selection, and recursive child construction. This is the heart of
// the tree building algorithm.
//
// Args:
// - view: DatasetView containing samples for this node (must be non-empty)
// - featureTypes: Map of available features and their types
// - depth: Current depth in tree (0 = root, used for max depth stopping criterion)
//
// Returns: Constructed Node (LeafNode or DecisionNode) or error if construction fails.
// Constraints: view must be non-empty, depth must be non-negative.
// Performance: O(n log n * f) where n=samples in view, f=number of features.
// Relationships: Recursively calls itself for child nodes, uses SplitEvaluator for splits.
// Side effects: Updates tree statistics, logs node construction progress.
//
// Algorithm:
// 1. Check stopping criteria (depth, samples, purity)
// 2. If stop: create LeafNode with majority class
// 3. If continue: find best split across all features
// 4. If no good split found: create LeafNode
// 5. If good split found: create DecisionNode and recurse for children
//
// Example: Internal method called by BuildTree and recursively by itself.
func (tb *TreeBuilder) buildNode(
	view *dataset.DatasetView[string],
	featureTypes map[string]features.FeatureType,
	depth int,
) (tree.Node, error) {
	if view == nil || view.GetSize() == 0 {
		return nil, fmt.Errorf("cannot build node from empty view")
	}

	// Update depth statistics
	if depth > tb.treeStats.MaxDepth {
		tb.treeStats.MaxDepth = depth
	}

	// Get target distribution for this node
	targetDist := view.GetTargetDistribution()
	samples := view.GetSize()

	logger.Debug(fmt.Sprintf("Building node at depth %d with %d samples, distribution: %v",
		depth, samples, targetDist))

	// Check stopping criteria
	if shouldStop(view, targetDist, depth, tb.config) {
		// Create leaf node
		leafNode, err := tb.createLeafNode(targetDist, samples)
		if err != nil {
			return nil, fmt.Errorf("failed to create leaf node: %w", err)
		}

		tb.treeStats.LeafNodes++
		tb.treeStats.TotalNodes++

		logger.Debug(fmt.Sprintf("Created leaf node at depth %d with prediction: %v (confidence: %.3f)",
			depth, leafNode.GetMajorityClass(), leafNode.GetConfidence()))

		return leafNode, nil
	}

	// Find best split across all features
	bestSplit, err := findBestSplit(view, featureTypes, tb.splitFactory)
	if err != nil {
		return nil, fmt.Errorf("failed to find best split: %w", err)
	}

	// If no good split found, create leaf node
	if bestSplit == nil || bestSplit.Gain <= tb.config.MinGainThreshold {
		logger.Debug(fmt.Sprintf("No beneficial split found (gain: %.6f <= threshold: %.6f), creating leaf",
			getGainSafe(bestSplit), tb.config.MinGainThreshold))

		leafNode, err := tb.createLeafNode(targetDist, samples)
		if err != nil {
			return nil, fmt.Errorf("failed to create leaf node: %w", err)
		}

		tb.treeStats.LeafNodes++
		tb.treeStats.TotalNodes++
		return leafNode, nil
	}

	// Create decision node with best split
	decisionNode, err := tb.createDecisionNode(bestSplit, targetDist, samples)
	if err != nil {
		return nil, fmt.Errorf("failed to create decision node: %w", err)
	}

	logger.Debug(fmt.Sprintf("Created decision node at depth %d: %s (gain: %.6f)",
		depth, formatSplit(bestSplit), bestSplit.Gain))

	// Apply split to create child views
	leftView, rightView, err := bestSplit.ApplySplit(view)
	if err != nil {
		return nil, fmt.Errorf("failed to apply split: %w", err)
	}

	// Validate child views have samples
	if leftView.GetSize() == 0 || rightView.GetSize() == 0 {
		logger.Debug("Split resulted in empty child, creating leaf instead")
		leafNode, err := tb.createLeafNode(targetDist, samples)
		if err != nil {
			return nil, fmt.Errorf("failed to create leaf node: %w", err)
		}

		tb.treeStats.LeafNodes++
		tb.treeStats.TotalNodes++
		return leafNode, nil
	}

	// Recursively build child nodes
	leftChild, err := tb.buildNode(leftView, featureTypes, depth+1)
	if err != nil {
		return nil, fmt.Errorf("failed to build left child: %w", err)
	}

	rightChild, err := tb.buildNode(rightView, featureTypes, depth+1)
	if err != nil {
		return nil, fmt.Errorf("failed to build right child: %w", err)
	}

	// Set children in decision node
	decisionNode.SetChild(tree.LeftBranch, leftChild)
	decisionNode.SetChild(tree.RightBranch, rightChild)

	tb.treeStats.DecisionNodes++
	tb.treeStats.TotalNodes++

	return decisionNode, nil
}

// createLeafNode creates a leaf node from target distribution and sample count.
//
// Constructs a terminal node containing the majority class prediction and
// confidence score based on the target class distribution at this node.
//
// Args:
// - targetDist: Distribution of target classes at this node
// - samples: Total number of samples that reached this node
//
// Returns: LeafNode with majority class prediction and confidence.
// Constraints: targetDist must be non-empty, samples must be > 0.
// Performance: O(n) where n = number of unique target classes.
// Relationships: Creates tree.LeafNode with calculated statistics.
// Side effects: None (pure node creation).
//
// Calculation:
// - Prediction: Most frequent target class in distribution
// - Confidence: (majority_class_count / total_samples)
//
// Example: Internal method used by buildNode when stopping criteria met.
func (tb *TreeBuilder) createLeafNode(
	targetDist map[string]int,
	samples int,
) (*tree.LeafNode, error) {
	if len(targetDist) == 0 {
		return nil, fmt.Errorf("cannot create leaf node from empty target distribution")
	}
	if samples <= 0 {
		return nil, fmt.Errorf("cannot create leaf node with %d samples", samples)
	}

	// Find majority class and its count
	var majorityClass string
	var majorityCount int

	for class, count := range targetDist {
		if count > majorityCount {
			majorityClass = class
			majorityCount = count
		}
	}

	// Calculate confidence as proportion of majority class
	confidence := float64(majorityCount) / float64(samples)

	// Convert target distribution to interface{} map for LeafNode
	classDist := make(map[interface{}]int)
	for class, count := range targetDist {
		classDist[class] = count
	}

	// Create leaf node
	leafNode, err := tree.NewLeafNode(classDist)
	if err != nil {
		return nil, fmt.Errorf("failed to create leaf node: %w", err)
	}

	logger.Debug(fmt.Sprintf("Created leaf node: prediction=%s, confidence=%.3f, samples=%d",
		majorityClass, confidence, samples))

	return leafNode, nil
}

// createDecisionNode creates a decision node from split candidate and node statistics.
//
// Constructs an internal node containing the split condition and node statistics.
// Child nodes will be set separately after recursive construction.
//
// Args:
// - split: SplitCandidate containing split condition and feature information
// - targetDist: Target class distribution at this node (before splitting)
// - samples: Total number of samples that reached this node
//
// Returns: DecisionNode with split condition and statistics.
// Constraints: split must be valid, targetDist non-empty, samples > 0.
// Performance: O(1) node creation with feature lookup.
// Relationships: Creates tree.DecisionNode with tree.Feature reference.
// Side effects: None (pure node creation, children set separately).
//
// Example: Internal method used by buildNode when split is beneficial.
func (tb *TreeBuilder) createDecisionNode(
	split *SplitCandidate,
	targetDist map[string]int,
	samples int,
) (*tree.DecisionNode, error) {
	if split == nil {
		return nil, fmt.Errorf("cannot create decision node from nil split")
	}
	if len(targetDist) == 0 {
		return nil, fmt.Errorf("cannot create decision node from empty target distribution")
	}
	if samples <= 0 {
		return nil, fmt.Errorf("cannot create decision node with %d samples", samples)
	}

	// Create feature for this split
	var featureType features.FeatureType
	switch split.Type {
	case NumericalSplit:
		// For numerical splits, we need to determine if it's int or float
		// Default to float for numerical splits (threshold is always float64)
		featureType = features.FloatFeature
	case CategoricalSplit:
		featureType = features.StringFeature
	default:
		return nil, fmt.Errorf("unsupported split type: %v", split.Type)
	}

	feature, err := tree.NewFeature(split.FeatureName, featureType)
	if err != nil {
		return nil, fmt.Errorf("failed to create feature for split: %w", err)
	}

	// Determine split value based on split type
	var splitValue interface{}
	switch split.Type {
	case NumericalSplit:
		if split.Threshold == nil {
			return nil, fmt.Errorf("numerical split missing threshold")
		}
		splitValue = *split.Threshold
	case CategoricalSplit:
		if split.Value == nil {
			return nil, fmt.Errorf("categorical split missing value")
		}
		splitValue = split.Value
	}

	// Convert target distribution to interface{} map for DecisionNode
	classDist := make(map[interface{}]int)
	for class, count := range targetDist {
		classDist[class] = count
	}

	// Create decision node
	decisionNode, err := tree.NewDecisionNode(feature, splitValue, classDist)
	if err != nil {
		return nil, fmt.Errorf("failed to create decision node: %w", err)
	}

	logger.Debug(fmt.Sprintf("Created decision node: feature=%s, split_value=%v, samples=%d",
		split.FeatureName, splitValue, samples))

	return decisionNode, nil
}

// ====================
// Tree Statistics Helpers
// ====================

// calculateFinalStatistics computes final tree statistics after construction.
//
// Traverses the completed tree to calculate comprehensive statistics including
// average depth, node counts, and other tree metrics.
//
// Args:
// - root: Root node of the completed tree
//
// Returns: None (updates tb.treeStats in place).
// Constraints: root must be valid tree node.
// Performance: O(n) where n = number of nodes in tree.
// Relationships: Updates TreeBuilder's treeStats field.
// Side effects: Modifies treeStats with calculated values.
//
// Example: Internal method called after tree construction completes.
func (tb *TreeBuilder) calculateFinalStatistics(root tree.Node) {
	if root == nil {
		logger.Error("Cannot calculate statistics for nil root")
		return
	}

	// Calculate average depth by traversing all leaf nodes
	var totalDepth int
	var leafCount int

	tb.calculateDepthStatistics(root, 0, &totalDepth, &leafCount)

	if leafCount > 0 {
		tb.treeStats.AvgDepth = float64(totalDepth) / float64(leafCount)
	} else {
		tb.treeStats.AvgDepth = 0.0
	}

	logger.Debug(fmt.Sprintf("Final tree statistics: total_nodes=%d, leaf_nodes=%d, decision_nodes=%d, max_depth=%d, avg_depth=%.2f",
		tb.treeStats.TotalNodes, tb.treeStats.LeafNodes, tb.treeStats.DecisionNodes,
		tb.treeStats.MaxDepth, tb.treeStats.AvgDepth))
}

// calculateDepthStatistics recursively calculates depth statistics for tree.
//
// Helper method for calculateFinalStatistics that traverses tree and
// accumulates depth information for average depth calculation.
//
// Args:
// - node: Current node in traversal
// - depth: Current depth (0 = root)
// - totalDepth: Accumulator for sum of all leaf depths
// - leafCount: Accumulator for number of leaf nodes
//
// Returns: None (updates accumulators by reference).
// Performance: O(n) tree traversal.
// Relationships: Recursive helper for calculateFinalStatistics.
// Side effects: Updates totalDepth and leafCount accumulators.
func (tb *TreeBuilder) calculateDepthStatistics(
	node tree.Node,
	depth int,
	totalDepth *int,
	leafCount *int,
) {
	if node == nil {
		return
	}

	if node.IsLeaf() {
		*totalDepth += depth
		*leafCount++
		return
	}

	// For decision nodes, recurse to children
	if decisionNode, ok := node.(*tree.DecisionNode); ok {
		leftChild := decisionNode.GetChild(tree.LeftBranch)
		rightChild := decisionNode.GetChild(tree.RightBranch)

		if leftChild != nil {
			tb.calculateDepthStatistics(leftChild, depth+1, totalDepth, leafCount)
		}
		if rightChild != nil {
			tb.calculateDepthStatistics(rightChild, depth+1, totalDepth, leafCount)
		}
	}
}
