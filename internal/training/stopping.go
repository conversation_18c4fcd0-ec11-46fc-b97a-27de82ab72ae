package training

import (
	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// shouldStop evaluates stopping criteria to determine if node should be a leaf.
//
// Implements the C4.5 stopping criteria including maximum depth, minimum samples
// for splitting, and node purity. Returns true if any stopping condition is met.
//
// Args:
// - view: DatasetView containing samples for current node
// - targetDist: Target class distribution at current node
// - depth: Current depth in tree (0 = root)
// - config: BuildConfig containing stopping criteria parameters
//
// Returns: True if node should be a leaf, false if splitting should continue.
// Constraints: view must be non-empty, depth must be non-negative.
// Performance: O(1) criteria evaluation.
// Relationships: Used by buildNode to determine leaf vs decision node creation.
// Side effects: Logs stopping reason when criteria met.
//
// Stopping Criteria:
// 1. Maximum depth reached (if MaxDepth > 0)
// 2. Insufficient samples for splitting (< MinSamplesSplit)
// 3. Node is pure (all samples have same target class)
// 4. Insufficient samples for meaningful leaves (< 2 * MinSamplesLeaf)
//
// Example: Internal method used during tree construction.
func shouldStop(
	view *dataset.DatasetView[string],
	targetDist map[string]int,
	depth int,
	config BuildConfig,
) bool {
	samples := view.GetSize()

	// Check maximum depth (if configured)
	if config.MaxDepth > 0 && depth >= config.MaxDepth {
		logger.Debug("Stopping: maximum depth reached")
		return true
	}

	// Check minimum samples for splitting
	if samples < config.MinSamplesSplit {
		logger.Debug("Stopping: insufficient samples for splitting")
		return true
	}

	// Check node purity (all samples have same target class)
	if len(targetDist) <= 1 {
		logger.Debug("Stopping: node is pure")
		return true
	}

	// Check if we have enough samples to create meaningful child leaves
	// Need at least 2 * MinSamplesLeaf to potentially create two valid children
	if samples < 2*config.MinSamplesLeaf {
		logger.Debug("Stopping: insufficient samples for meaningful child leaves")
		return true
	}

	return false
}
