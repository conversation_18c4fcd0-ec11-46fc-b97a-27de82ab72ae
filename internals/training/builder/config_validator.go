package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultConfigValidator provides the default implementation of ConfigValidator interface
type DefaultConfigValidator struct {
	config TreeConfiguration
}

// NewDefaultConfigValidator creates a new default config validator
func NewDefaultConfigValidator(config TreeConfiguration) *DefaultConfigValidator {
	return &DefaultConfigValidator{
		config: config,
	}
}

// ValidateConfiguration validates the tree building configuration
func (cv *DefaultConfigValidator) ValidateConfiguration() error {
	// Validate relationship between min samples split and leaf
	if cv.config.GetMinSamplesLeaf() > cv.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:    "validate_configuration",
			Field: "min_samples_leaf",
			Value: fmt.Sprintf("%d", cv.config.GetMinSamplesLeaf()),
			Reason: fmt.Sprintf("min_samples_leaf (%d) cannot be greater than min_samples_split (%d)",
				cv.config.GetMinSamplesLeaf(), cv.config.GetMinSamplesSplit()),
		}
	}

	// Validate min impurity decrease
	if cv.config.GetMinImpurityDecrease() < 0.0 {
		return &BuilderError{
			Op:     "validate_configuration",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", cv.config.GetMinImpurityDecrease()),
			Reason: "min impurity decrease cannot be negative",
		}
	}

	// Validate target type
	targetType := cv.config.GetTargetType()
	if !isValidTargetType(targetType) {
		return &BuilderError{
			Op:     "validate_configuration",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	return nil
}

// ValidateCompatibility validates compatibility between configuration and type
func (cv *DefaultConfigValidator) ValidateCompatibility() error {
	// This is a placeholder for type-specific validation
	// In a real implementation, this would validate that the generic type T
	// is compatible with the configured target type
	return nil
}

// isValidTargetType checks if the target type is valid
func isValidTargetType(targetType models.FeatureType) bool {
	switch targetType {
	case models.NumericFeature, models.CategoricalFeature, models.DateTimeFeature:
		return true
	default:
		return false
	}
}

// ValidateOptionConsistency validates that option combinations are consistent
func (cv *DefaultConfigValidator) ValidateOptionConsistency() error {
	// Check for unrealistic combinations
	if cv.config.GetMaxDepth() > 50 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", cv.config.GetMaxDepth()),
			Reason: "max depth greater than 50 may cause performance issues",
		}
	}

	if cv.config.GetMinSamplesSplit() > 10000 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "min_samples_split",
			Value:  fmt.Sprintf("%d", cv.config.GetMinSamplesSplit()),
			Reason: "min samples split greater than 10000 may be too restrictive",
		}
	}

	if cv.config.GetMinImpurityDecrease() > 0.5 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", cv.config.GetMinImpurityDecrease()),
			Reason: "min impurity decrease greater than 0.5 may prevent any splits",
		}
	}

	return nil
}

// GetConfigurationSummary returns a human-readable summary of the configuration
func (cv *DefaultConfigValidator) GetConfigurationSummary() string {
	return fmt.Sprintf(
		"TreeConfig{MaxDepth: %d, MinSamplesSplit: %d, MinSamplesLeaf: %d, MinImpurityDecrease: %.3f, Criterion: %s, TargetType: %s, Logging: %t}",
		cv.config.GetMaxDepth(),
		cv.config.GetMinSamplesSplit(),
		cv.config.GetMinSamplesLeaf(),
		cv.config.GetMinImpurityDecrease(),
		string(cv.config.GetCriterion()),
		string(cv.config.GetTargetType()),
		cv.config.GetEnableLogging(),
	)
}

// ValidateForDataset validates configuration against dataset characteristics
func (cv *DefaultConfigValidator) ValidateForDataset(datasetSize int, featureCount int) error {
	if datasetSize < cv.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:    "validate_for_dataset",
			Field: "dataset_size",
			Value: fmt.Sprintf("%d", datasetSize),
			Reason: fmt.Sprintf("dataset size (%d) is smaller than min_samples_split (%d)",
				datasetSize, cv.config.GetMinSamplesSplit()),
		}
	}

	if featureCount == 0 {
		return &BuilderError{
			Op:     "validate_for_dataset",
			Field:  "feature_count",
			Value:  "0",
			Reason: "cannot build tree with zero features",
		}
	}

	// Warn about potential overfitting
	if cv.config.GetMinSamplesLeaf() == 1 && datasetSize < 100 {
		// This is just a warning, not an error
		// In a real implementation, you might use a logger here
	}

	return nil
}
