package utils

import (
	"os"
	"path/filepath"
	"reflect"
	"testing"
)

func createTempCSVFile(t *testing.T, content string) string {
	tempDir, err := os.MkdirTemp("", "csv_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	tempFile := filepath.Join(tempDir, "test.csv")
	err = os.WriteFile(tempFile, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	t.Cleanup(func() {
		os.RemoveAll(tempDir)
	})

	return tempFile
}

func TestReadTrainingCSV_ValidFile(t *testing.T) {
	csvContent := `record1,record2,record3,target
1,2,3,A
4,5,6,B
7,8,9,C`
	tempFile := createTempCSVFile(t, csvContent)

	_, data, targetColumnIndex, err := ReadTrainingCSV(tempFile, "target")
	if err != nil {
		t.<PERSON><PERSON>rf("ReadTrainingCSV returned an error for valid file: %v", err)
	}

	expected := [][]string{
		{"1", "2", "3", "A"},
		{"4", "5", "6", "B"},
		{"7", "8", "9", "C"},
	}

	if !reflect.DeepEqual(data, expected) {
		t.Errorf("ReadTrainingCSV returned incorrect data.\nExpected: %v\nGot: %v", expected, data)
	}

	// Verify target index
	if targetColumnIndex != 3 {
		t.Errorf("ReadTrainingCSV returned incorrect target index.\nExpected: %d\nGot: %d", 3, targetColumnIndex)
	}
}

func TestReadTrainingCSV_NonExistentFile(t *testing.T) {
	_, _, _, err := ReadTrainingCSV("non_existent_file.csv", "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for non-existent file")
	}

	// Test structured error
	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "open" {
			t.Errorf("Expected CSV error with 'open' operation, got: %s", csvErr.Op)
		}
	} else {
		t.Errorf("Expected CSVError, got: %T", err)
	}
}

func TestReadTrainingCSV_EmptyFile(t *testing.T) {
	tempFile := createTempCSVFile(t, "")

	_, _, _, err := ReadTrainingCSV(tempFile, "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for empty file")
	}

	// Test structured error
	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "read_headers" {
			t.Errorf("Expected CSV error with 'read_headers' operation, got: %s", csvErr.Op)
		}
	}
}

func TestReadTrainingCSV_HeadersOnlyFile(t *testing.T) {
	csvContent := "record1,record2,record3,target"
	tempFile := createTempCSVFile(t, csvContent)

	_, _, _, err := ReadTrainingCSV(tempFile, "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for file with only headers")
	}

	// Test structured error
	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "validate_data_size" {
			t.Errorf("Expected CSV error with 'validate_data_size' operation, got: %s", csvErr.Op)
		}
	}
}

func TestReadTrainingCSV_MissingTargetColumn(t *testing.T) {
	csvContent := `record1,record2,record3
1,2,3
4,5,6`
	tempFile := createTempCSVFile(t, csvContent)

	_, _, _, err := ReadTrainingCSV(tempFile, "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for missing target column")
	}

	// Test structured error
	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "target_column" {
			t.Errorf("Expected validation error for target_column, got: %s", validationErr.Field)
		}
	}
}

func TestReadTrainingCSV_EmptyTargetColumn(t *testing.T) {
	csvContent := `record1,record2,record3,target
1,2,3,A
4,5,6,
7,8,9,C`
	tempFile := createTempCSVFile(t, csvContent)

	_, _, _, err := ReadTrainingCSV(tempFile, "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for empty target values")
	}

	// Test structured error
	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "validate_target" {
			t.Errorf("Expected CSV error with 'validate_target' operation, got: %s", csvErr.Op)
		}
		if csvErr.Line != 3 {
			t.Errorf("Expected error at line 3, got: %d", csvErr.Line)
		}
	}
}

func TestReadTrainingCSV_NATargetValues(t *testing.T) {
	// Test with various NA values in the target column
	naValues := []string{"NA", "na", "NaN", "nan", "N/A", "n/a", "NULL", "null", "NONE", "none"}

	for _, naValue := range naValues {
		t.Run("NA_value_"+naValue, func(t *testing.T) {
			csvContent := `record1,record2,record3,target
1,2,3,A
4,5,6,` + naValue + `
7,8,9,C`
			tempFile := createTempCSVFile(t, csvContent)

			_, _, _, err := ReadTrainingCSV(tempFile, "target")
			if err == nil {
				t.Errorf("ReadTrainingCSV should return an error for NA value '%s' in target column", naValue)
			}

			// Test structured error
			if csvErr, ok := err.(*CSVError); ok {
				if csvErr.Op != "validate_target" {
					t.Errorf("Expected CSV error with 'validate_target' operation, got: %s", csvErr.Op)
				}
			}
		})
	}
}

func TestReadTrainingCSV_EmptyHeaderField(t *testing.T) {
	csvContent := `record1,,record3,target
1,2,3,A
4,5,6,B
7,8,9,C`
	tempFile := createTempCSVFile(t, csvContent)

	_, _, _, err := ReadTrainingCSV(tempFile, "target")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for empty header field")
	}

	// Test structured error
	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "validate_headers" {
			t.Errorf("Expected CSV error with 'validate_headers' operation, got: %s", csvErr.Op)
		}
	}
}

func TestReadTrainingCSV_QuotedFields(t *testing.T) {
	csvContent := `record1,record2,record3,target
1,"Hello, world",3,A
4,"quoted ""value""",6,B
7,"line1
line2",9,C`
	tempFile := createTempCSVFile(t, csvContent)

	_, data, targetColumnIndex, err := ReadTrainingCSV(tempFile, "target")
	if err != nil {
		t.Errorf("ReadTrainingCSV returned an error for valid quoted fields: %v", err)
	}

	expected := [][]string{
		{"1", "Hello, world", "3", "A"},
		{"4", "quoted \"value\"", "6", "B"},
		{"7", "line1\nline2", "9", "C"},
	}

	if !reflect.DeepEqual(data, expected) {
		t.Errorf("ReadTrainingCSV returned incorrect data for quoted fields.\nExpected: %v\nGot: %v", expected, data)
	}

	// Check that target index is correct
	if targetColumnIndex != 3 {
		t.Errorf("ReadTrainingCSV returned incorrect target index.\nExpected: %d\nGot: %d", 3, targetColumnIndex)
	}
}

func TestReadTrainingCSV_EmptyTargetColumnName(t *testing.T) {
	csvContent := `record1,record2,record3,target
1,2,3,A
4,5,6,B`
	tempFile := createTempCSVFile(t, csvContent)

	_, _, _, err := ReadTrainingCSV(tempFile, "")
	if err == nil {
		t.Errorf("ReadTrainingCSV should return an error for empty target column name")
	}

	// Test structured error
	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "target_column" {
			t.Errorf("Expected validation error for target_column, got: %s", validationErr.Field)
		}
	}
}

func TestExtractRecordsAndTarget_ValidData(t *testing.T) {
	headers := []string{"record1", "record2", "target", "record3"}
	data := [][]string{
		{"1", "2", "A", "3"},
		{"4", "5", "B", "6"},
		{"7", "8", "C", "9"},
	}
	targetColumnIndex := 2 // "target" column is at index 2

	records, targets, featureHeaders, err := ExtractRecordsAndTarget(data, headers, "target", targetColumnIndex)
	if err != nil {
		t.Errorf("ExtractRecordsAndTarget returned an error for valid data: %v", err)
	}

	expectedRecords := [][]string{
		{"1", "2", "3"},
		{"4", "5", "6"},
		{"7", "8", "9"},
	}
	expectedTargets := []string{"A", "B", "C"}
	expectedFeatureHeaders := []string{"record1", "record2", "record3"}

	if !reflect.DeepEqual(records, expectedRecords) {
		t.Errorf("ExtractRecordsAndTarget returned incorrect records.\nExpected: %v\nGot: %v", expectedRecords, records)
	}

	if !reflect.DeepEqual(targets, expectedTargets) {
		t.Errorf("ExtractRecordsAndTarget returned incorrect targets.\nExpected: %v\nGot: %v", expectedTargets, targets)
	}

	if !reflect.DeepEqual(featureHeaders, expectedFeatureHeaders) {
		t.Errorf("ExtractRecordsAndTarget returned incorrect feature headers.\nExpected: %v\nGot: %v", expectedFeatureHeaders, featureHeaders)
	}
}

func TestExtractRecordsAndTarget_MissingTargetColumn(t *testing.T) {
	headers := []string{"record1", "record2", "record3"}
	data := [][]string{
		{"1", "2", "3"},
		{"4", "5", "6"},
	}
	targetColumnIndex := -1 // Invalid target index

	_, _, _, err := ExtractRecordsAndTarget(data, headers, "target", targetColumnIndex)
	if err == nil {
		t.Errorf("ExtractRecordsAndTarget should return an error for missing target column")
	}

	// Test structured error
	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "target_column" {
			t.Errorf("Expected validation error for target_column, got: %s", validationErr.Field)
		}
	}
}

func TestExtractRecordsAndTarget_EmptyData(t *testing.T) {
	headers := []string{"record1", "record2", "target"}
	data := [][]string{} // Empty data
	targetColumnIndex := 2

	_, _, _, err := ExtractRecordsAndTarget(data, headers, "target", targetColumnIndex)
	if err == nil {
		t.Errorf("ExtractRecordsAndTarget should return an error for empty data")
	}

	// Test structured error
	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "data" {
			t.Errorf("Expected validation error for data, got: %s", validationErr.Field)
		}
	}
}

func TestExtractRecordsAndTarget_InconsistentRowLength(t *testing.T) {
	headers := []string{"record1", "record2", "target"}
	data := [][]string{
		{"1", "2", "A"},
		{"4", "5"}, // Missing target value
		{"7", "8", "C"},
	}
	targetColumnIndex := 2

	_, _, _, err := ExtractRecordsAndTarget(data, headers, "target", targetColumnIndex)
	if err == nil {
		t.Errorf("ExtractRecordsAndTarget should return an error for inconsistent row lengths")
	}

	// Test structured error
	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "row_length" {
			t.Errorf("Expected validation error for row_length, got: %s", validationErr.Field)
		}
	}
}

func TestReadTrainingCSV_FullFlow(t *testing.T) {
	csvContent := `record1,record2,record3,target
1,2,3,A
4,5,6,B
7,8,9,C`
	tempFile := createTempCSVFile(t, csvContent)

	headers, data, targetColumnIndex, err := ReadTrainingCSV(tempFile, "target")
	if err != nil {
		t.Fatalf("ReadTrainingCSV returned an error: %v", err)
	}

	// Extract records and targets
	records, targets, featureHeaders, err := ExtractRecordsAndTarget(data, headers, "target", targetColumnIndex)
	if err != nil {
		t.Fatalf("ExtractRecordsAndTarget returned an error: %v", err)
	}

	expectedRecords := [][]string{
		{"1", "2", "3"},
		{"4", "5", "6"},
		{"7", "8", "9"},
	}
	expectedTargets := []string{"A", "B", "C"}
	expectedFeatureHeaders := []string{"record1", "record2", "record3"}

	if !reflect.DeepEqual(records, expectedRecords) {
		t.Errorf("Full flow returned incorrect records.\nExpected: %v\nGot: %v", expectedRecords, records)
	}

	if !reflect.DeepEqual(targets, expectedTargets) {
		t.Errorf("Full flow returned incorrect targets.\nExpected: %v\nGot: %v", expectedTargets, targets)
	}

	if !reflect.DeepEqual(featureHeaders, expectedFeatureHeaders) {
		t.Errorf("Full flow returned incorrect feature headers.\nExpected: %v\nGot: %v", expectedFeatureHeaders, featureHeaders)
	}
}

func TestIsNAValue(t *testing.T) {
	// Define test cases with inputs and expected outputs
	testCases := []struct {
		input    string
		expected bool
		desc     string
	}{
		// Positive test cases - these should return true
		{"na", true, "lowercase na"},
		{"NA", true, "uppercase NA"},
		{"Na", true, "mixed case Na"},
		{"nan", true, "lowercase nan"},
		{"NaN", true, "mixed case NaN"},
		{"NAN", true, "uppercase NAN"},
		{"n/a", true, "lowercase n/a"},
		{"N/A", true, "uppercase N/A"},
		{"null", true, "lowercase null"},
		{"NULL", true, "uppercase NULL"},
		{"Null", true, "mixed case Null"},
		{"none", true, "lowercase none"},
		{"NONE", true, "uppercase NONE"},
		{"None", true, "mixed case None"},
		{"missing", true, "lowercase missing"},
		{"MISSING", true, "uppercase MISSING"},
		{"unknown", true, "lowercase unknown"},
		{"UNKNOWN", true, "uppercase UNKNOWN"},
		{"undefined", true, "lowercase undefined"},
		{"UNDEFINED", true, "uppercase UNDEFINED"},
		{"", true, "empty string"},

		// Test with whitespace
		{" na ", true, "na with spaces"},
		{"\tnan\t", true, "nan with tabs"},
		{" N/A ", true, "N/A with spaces"},
		{" null ", true, "null with spaces"},
		{"\tnone\n", true, "none with whitespace"},

		// Negative test cases - these should return false
		{"0", false, "zero"},
		{"false", false, "false"},
		{"n a", false, "n a with space"},
		{"not-a-number", false, "not-a-number"},
		{"n-a", false, "n-a with dash"},
		{"n.a", false, "n.a with dot"},
		{"nann", false, "nann (extra n)"},
		{"naa", false, "naa (extra a)"},
		{"nulll", false, "nulll (extra l)"},
		{"nonee", false, "nonee (extra e)"},
		{"n/a/", false, "n/a/ (extra /)"},
		{"valid_data", false, "valid data value"},
	}

	// Run through all test cases
	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			result := isNAValue(tc.input)
			if result != tc.expected {
				t.Errorf("isNAValue(%q) = %v; expected %v", tc.input, result, tc.expected)
			}
		})
	}
}

// TestIsNAValueEdgeCases tests edge cases for the isNAValue function
func TestIsNAValueEdgeCases(t *testing.T) {
	// Additional edge cases
	edgeCases := []struct {
		input    string
		expected bool
		desc     string
	}{
		{" ", true, "just a space"},
		{"\t", true, "just a tab"},
		{"\n", true, "just a newline"},
		{"n//a", false, "n//a with double slash"},
		{"n\\a", false, "n\\a with backslash"},
		{"nAn", true, "nAn with internal capital"},
		{"NONe", true, "NONe with mixed case"},
		{"NuLl", true, "NuLl with odd capitalization"},
	}

	for _, tc := range edgeCases {
		t.Run(tc.desc, func(t *testing.T) {
			result := isNAValue(tc.input)
			if result != tc.expected {
				t.Errorf("isNAValue(%q) = %v; expected %v", tc.input, result, tc.expected)
			}
		})
	}
}

// Test error unwrapping functionality
func TestCSVError_Unwrap(t *testing.T) {
	originalErr := os.ErrNotExist
	csvErr := &CSVError{
		Op:   "open",
		File: "test.csv",
		Err:  originalErr,
	}

	if csvErr.Unwrap() != originalErr {
		t.Errorf("CSVError.Unwrap() should return the original error")
	}
}

func TestValidationError_Message(t *testing.T) {
	err := &ValidationError{
		Field:  "target_column",
		Value:  "invalid",
		Reason: "not found in headers",
	}

	expected := "validation failed for field 'target_column' with value 'invalid': not found in headers"
	if err.Error() != expected {
		t.Errorf("ValidationError.Error() = %q; expected %q", err.Error(), expected)
	}
}
