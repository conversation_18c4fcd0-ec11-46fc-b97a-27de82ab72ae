// Package utils provides utilities for loading prediction data for machine learning models.
package utils

import (
	"bufio"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
	"github.com/berrijam/mulberri/pkg/models"
)

// PredictionRecord represents a single record for prediction with typed feature values
type PredictionRecord struct {
	Features map[string]interface{} `json:"features"`  // Feature name -> typed value
	RowIndex int                    `json:"row_index"` // Original row index for tracking
}

// ReadPredictionCSV reads and validates a CSV file containing prediction data (without target column).
// It parses feature values according to their expected types and returns structured records
// ready for prediction.
//
// The function performs the following validations:
// - File must be accessible for reading
// - CSV must have headers
// - Headers must not be empty strings
// - Each row must have the same number of fields as the header
// - There must be at least 1 data row (excluding header)
// - <PERSON><PERSON>ly handles quoted fields (e.g., "Hello, world" is treated as a single field)
//
// Note: File extension validation (.csv) is expected to be performed by the caller.
//
// Parameters:
// - filePath: Path to the CSV file containing prediction data
// - expectedFeatures: Map of feature names to their expected types for validation and parsing
//
// Returns:
// - []string: The headers from the CSV file
// - []PredictionRecord: Parsed and typed prediction records
// - error: Any error encountered during reading or validation
func ReadPredictionCSV(filePath string, expectedFeatures map[string]models.FeatureType) ([]string, []PredictionRecord, error) {
	// Validate input parameters
	if len(expectedFeatures) == 0 {
		return nil, nil, &ValidationError{
			Field:  "expected_features",
			Value:  "empty",
			Reason: "expected features map cannot be empty",
		}
	}

	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "open",
			File: filePath,
			Err:  err,
		}
	}
	defer file.Close()

	// Get file info for optimization
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "stat",
			File: filePath,
			Err:  err,
		}
	}

	// Estimate number of rows for pre-allocation. this reduces memory reallocation when reading the csv file But it can be resized as you append more files.
	estimatedRows := int(fileInfo.Size() / 100) // Rough estimate: 100 bytes per row
	if estimatedRows < 1 {
		estimatedRows = 10
	}

	// Process the CSV file with buffered reader for better performance
	reader := csv.NewReader(bufio.NewReader(file))

	// Configure the CSV reader
	reader.FieldsPerRecord = -1
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = false // Enforce strict quotes (RFC 4180 compliant)
	reader.Comma = DefaultDelimiter

	// Read headers
	headers, err := reader.Read()
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "read_headers",
			File: filePath,
			Err:  err,
		}
	}

	// Validate headers using shared validation logic
	if err := validateHeaders(headers, filePath); err != nil {
		return nil, nil, err
	}

	// Validate that all expected features are present in headers
	headerSet := make(map[string]int)
	for i, header := range headers {
		headerSet[header] = i
	}

	for featureName := range expectedFeatures {
		if _, exists := headerSet[featureName]; !exists {
			return nil, nil, &ValidationError{
				Field:  "missing_feature",
				Value:  featureName,
				Reason: "expected feature not found in CSV headers",
			}
		}
	}

	reader.FieldsPerRecord = len(headers)

	// Pre-allocate records slice with estimated size
	records := make([]PredictionRecord, 0, estimatedRows)
	// lineNum starts at 1 to account for the header row already read
	lineNum := 1

	for {
		lineNum++
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, nil, &CSVError{
				Op:   "parse_record",
				File: filePath,
				Line: lineNum,
				Err:  err,
			}
		}

		// Parse and validate the record
		predictionRecord, err := parsePredictionRecord(record, headers, expectedFeatures, lineNum)
		if err != nil {
			return nil, nil, &CSVError{
				Op:   "parse_prediction_record",
				File: filePath,
				Line: lineNum,
				Err:  err,
			}
		}

		predictionRecord.RowIndex = lineNum - 2 // Adjust for header and 0-based indexing
		records = append(records, predictionRecord)
	}

	// Check if we have any data
	if len(records) == 0 {
		return nil, nil, &CSVError{
			Op:   "validate_data_size",
			File: filePath,
			Err:  errors.New("no data rows found in CSV file"),
		}
	}

	return headers, records, nil
}

// parsePredictionRecord parses a single CSV record into a typed PredictionRecord
func parsePredictionRecord(record []string, headers []string, expectedFeatures map[string]models.FeatureType, lineNum int) (PredictionRecord, error) {
	features := make(map[string]interface{})

	for i, value := range record {
		if i >= len(headers) {
			return PredictionRecord{}, fmt.Errorf("record has more fields than headers")
		}

		featureName := headers[i]
		expectedType, isExpected := expectedFeatures[featureName]

		// Skip features that are not expected (e.g., extra columns in CSV)
		if !isExpected {
			continue
		}

		// Handle missing/NA values
		if value == "" || isNAValue(value) {
			// For missing values, we store nil and let the prediction engine handle it
			features[featureName] = nil
			continue
		}

		// Parse according to expected type
		switch expectedType {
		case models.NumericFeature:
			parsedValue, err := strconv.ParseFloat(strings.TrimSpace(value), 64)
			if err != nil {
				return PredictionRecord{}, fmt.Errorf("failed to parse numeric value '%s' for feature '%s': %w", value, featureName, err)
			}
			features[featureName] = parsedValue

		case models.CategoricalFeature:
			// Store categorical values as strings
			features[featureName] = strings.TrimSpace(value)

		case models.DateTimeFeature:
			// Convert datetime strings to integers at parse time for efficiency
			trimmedValue := strings.TrimSpace(value)
			converter := datetimeconverter.GetGlobalConverter() // Use singleton converter
			intValue, err := converter.ConvertISO8601ToInt(trimmedValue)
			if err != nil {
				return PredictionRecord{}, fmt.Errorf("failed to parse datetime value '%s' for feature '%s': %w", value, featureName, err)
			}
			features[featureName] = intValue

		default:
			return PredictionRecord{}, fmt.Errorf("unsupported feature type '%s' for feature '%s'", expectedType, featureName)
		}
	}

	return PredictionRecord{
		Features: features,
		RowIndex: 0, // Will be set by caller
	}, nil
}

// LoadPredictionDataFromModel is a convenience function that extracts feature schema from a trained model
// and loads prediction data accordingly.
//
// Parameters:
// - filePath: Path to the CSV file containing prediction data
// - model: Trained decision tree model containing feature schema
//
// Returns:
// - []string: The headers from the CSV file
// - []PredictionRecord: Parsed and typed prediction records
// - error: Any error encountered during reading or validation
func LoadPredictionDataFromModel(filePath string, model *models.DecisionTree) ([]string, []PredictionRecord, error) {
	// Basic safety checks to prevent panic and ensure model is usable
	if model == nil {
		return nil, nil, &ValidationError{
			Field:  "model",
			Value:  "nil",
			Reason: "model cannot be nil",
		}
	}

	if len(model.Features) == 0 {
		return nil, nil, &ValidationError{
			Field:  "model_features",
			Value:  "empty",
			Reason: "model must have features defined",
		}
	}

	// Extract feature types from model
	expectedFeatures := make(map[string]models.FeatureType)
	for name, feature := range model.Features {
		expectedFeatures[name] = feature.Type
	}

	return ReadPredictionCSV(filePath, expectedFeatures)
}
