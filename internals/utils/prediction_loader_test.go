package utils

import (
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

// Helper function to create temporary CSV files for testing
func createTempPredictionCSVFile(t *testing.T, content string) string {
	tmpDir := t.TempDir()
	tmpFile := filepath.Join(tmpDir, "test_prediction.csv")

	err := os.WriteFile(tmpFile, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	return tmpFile
}

func TestReadPredictionCSV_ValidFile(t *testing.T) {
	csvContent := `age,income,category
25,50000.5,A
30,75000.0,B
35,100000.25,C`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age":      models.NumericFeature,
		"income":   models.NumericFeature,
		"category": models.CategoricalFeature,
	}

	headers, records, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err != nil {
		t.<PERSON>rf("ReadPredictionCSV returned an error for valid file: %v", err)
	}

	expectedHeaders := []string{"age", "income", "category"}
	if !reflect.DeepEqual(headers, expectedHeaders) {
		t.Errorf("ReadPredictionCSV returned incorrect headers.\nExpected: %v\nGot: %v", expectedHeaders, headers)
	}

	if len(records) != 3 {
		t.Errorf("Expected 3 records, got %d", len(records))
	}

	// Check first record
	firstRecord := records[0]
	if firstRecord.RowIndex != 0 {
		t.Errorf("Expected RowIndex 0, got %d", firstRecord.RowIndex)
	}

	expectedAge := 25.0
	if age, ok := firstRecord.Features["age"].(float64); !ok || age != expectedAge {
		t.Errorf("Expected age %f, got %v", expectedAge, firstRecord.Features["age"])
	}

	expectedIncome := 50000.5
	if income, ok := firstRecord.Features["income"].(float64); !ok || income != expectedIncome {
		t.Errorf("Expected income %f, got %v", expectedIncome, firstRecord.Features["income"])
	}

	expectedCategory := "A"
	if category, ok := firstRecord.Features["category"].(string); !ok || category != expectedCategory {
		t.Errorf("Expected category %s, got %v", expectedCategory, firstRecord.Features["category"])
	}
}

func TestReadPredictionCSV_MissingFeature(t *testing.T) {
	csvContent := `age,income
25,50000
30,75000`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age":      models.NumericFeature,
		"income":   models.NumericFeature,
		"category": models.CategoricalFeature, // This feature is missing from CSV
	}

	_, _, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for missing feature")
	}

	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "missing_feature" {
			t.Errorf("Expected validation error for missing_feature, got: %s", validationErr.Field)
		}
		if validationErr.Value != "category" {
			t.Errorf("Expected missing feature 'category', got: %s", validationErr.Value)
		}
	} else {
		t.Errorf("Expected ValidationError, got: %T", err)
	}
}

func TestReadPredictionCSV_InvalidNumericValue(t *testing.T) {
	csvContent := `age,income,category
invalid_age,50000,A
30,75000,B`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age":      models.NumericFeature,
		"income":   models.NumericFeature,
		"category": models.CategoricalFeature,
	}

	_, _, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for invalid numeric value")
	}

	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "parse_prediction_record" {
			t.Errorf("Expected CSV error with 'parse_prediction_record' operation, got: %s", csvErr.Op)
		}
		if csvErr.Line != 2 {
			t.Errorf("Expected error at line 2, got: %d", csvErr.Line)
		}
	} else {
		t.Errorf("Expected CSVError, got: %T", err)
	}
}

func TestReadPredictionCSV_MissingValues(t *testing.T) {
	csvContent := `age,income,category
25,,A
,75000,B
35,100000,`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age":      models.NumericFeature,
		"income":   models.NumericFeature,
		"category": models.CategoricalFeature,
	}

	_, records, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err != nil {
		t.Errorf("ReadPredictionCSV returned an error for missing values: %v", err)
	}

	if len(records) != 3 {
		t.Errorf("Expected 3 records, got %d", len(records))
	}

	// Check that missing values are handled as nil
	if records[0].Features["income"] != nil {
		t.Errorf("Expected nil for missing income, got %v", records[0].Features["income"])
	}

	if records[1].Features["age"] != nil {
		t.Errorf("Expected nil for missing age, got %v", records[1].Features["age"])
	}

	if records[2].Features["category"] != nil {
		t.Errorf("Expected nil for missing category, got %v", records[2].Features["category"])
	}
}

func TestReadPredictionCSV_NAValues(t *testing.T) {
	naValues := []string{"na", "NA", "nan", "NaN", "n/a", "N/A", "null", "NULL", "none", "NONE"}

	for _, naValue := range naValues {
		t.Run("NA_value_"+naValue, func(t *testing.T) {
			csvContent := `age,income,category
25,50000,A
30,` + naValue + `,B
35,100000,C`
			tempFile := createTempPredictionCSVFile(t, csvContent)

			expectedFeatures := map[string]models.FeatureType{
				"age":      models.NumericFeature,
				"income":   models.NumericFeature,
				"category": models.CategoricalFeature,
			}

			_, records, err := ReadPredictionCSV(tempFile, expectedFeatures)
			if err != nil {
				t.Errorf("ReadPredictionCSV returned an error for NA value '%s': %v", naValue, err)
			}

			if records[1].Features["income"] != nil {
				t.Errorf("Expected nil for NA value '%s', got %v", naValue, records[1].Features["income"])
			}
		})
	}
}

func TestReadPredictionCSV_EmptyFile(t *testing.T) {
	csvContent := ``
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age": models.NumericFeature,
	}

	_, _, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for empty file")
	}
}

func TestReadPredictionCSV_NoDataRows(t *testing.T) {
	csvContent := `age,income,category`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	expectedFeatures := map[string]models.FeatureType{
		"age":      models.NumericFeature,
		"income":   models.NumericFeature,
		"category": models.CategoricalFeature,
	}

	_, _, err := ReadPredictionCSV(tempFile, expectedFeatures)
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for file with no data rows")
	}

	if csvErr, ok := err.(*CSVError); ok {
		if csvErr.Op != "validate_data_size" {
			t.Errorf("Expected CSV error with 'validate_data_size' operation, got: %s", csvErr.Op)
		}
	}
}

func TestReadPredictionCSV_EmptyExpectedFeatures(t *testing.T) {
	csvContent := `age,income
25,50000`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	_, _, err := ReadPredictionCSV(tempFile, nil)
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for nil expected features")
	}

	_, _, err = ReadPredictionCSV(tempFile, map[string]models.FeatureType{})
	if err == nil {
		t.Errorf("ReadPredictionCSV should return an error for empty expected features")
	}
}

func TestLoadPredictionDataFromModel_ValidModel(t *testing.T) {
	csvContent := `age,income,category
25,50000,A
30,75000,B`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	// Create a mock decision tree model
	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature),
	}

	// Add features to the model
	ageFeature, err := models.NewFeature("age", models.NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create age feature: %v", err)
	}
	model.Features["age"] = ageFeature

	incomeFeature, err := models.NewFeature("income", models.NumericFeature, 1)
	if err != nil {
		t.Fatalf("Failed to create income feature: %v", err)
	}
	model.Features["income"] = incomeFeature

	categoryFeature, err := models.NewFeature("category", models.CategoricalFeature, 2)
	if err != nil {
		t.Fatalf("Failed to create category feature: %v", err)
	}
	model.Features["category"] = categoryFeature

	headers, records, err := LoadPredictionDataFromModel(tempFile, model)
	if err != nil {
		t.Errorf("LoadPredictionDataFromModel returned an error: %v", err)
	}

	expectedHeaders := []string{"age", "income", "category"}
	if !reflect.DeepEqual(headers, expectedHeaders) {
		t.Errorf("LoadPredictionDataFromModel returned incorrect headers.\nExpected: %v\nGot: %v", expectedHeaders, headers)
	}

	if len(records) != 2 {
		t.Errorf("Expected 2 records, got %d", len(records))
	}
}

func TestLoadPredictionDataFromModel_NilModel(t *testing.T) {
	csvContent := `age,income
25,50000`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	_, _, err := LoadPredictionDataFromModel(tempFile, nil)
	if err == nil {
		t.Errorf("LoadPredictionDataFromModel should return an error for nil model")
	}

	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "model" {
			t.Errorf("Expected validation error for model, got: %s", validationErr.Field)
		}
	}
}

func TestLoadPredictionDataFromModel_EmptyModelFeatures(t *testing.T) {
	csvContent := `age,income
25,50000`
	tempFile := createTempPredictionCSVFile(t, csvContent)

	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature),
	}

	_, _, err := LoadPredictionDataFromModel(tempFile, model)
	if err == nil {
		t.Errorf("LoadPredictionDataFromModel should return an error for model with no features")
	}

	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "model_features" {
			t.Errorf("Expected validation error for model_features, got: %s", validationErr.Field)
		}
	}
}

// TestParsePredictionRecord_DateTimeConversion tests that datetime features are converted to integers at parse time
func TestParsePredictionRecord_DateTimeConversion(t *testing.T) {
	headers := []string{"timestamp", "event_type", "value"}
	record := []string{"2023-12-25T14:30:45Z", "login", "42.5"}

	expectedFeatures := map[string]models.FeatureType{
		"timestamp":  models.DateTimeFeature,
		"event_type": models.CategoricalFeature,
		"value":      models.NumericFeature,
	}

	result, err := parsePredictionRecord(record, headers, expectedFeatures, 1)
	if err != nil {
		t.Fatalf("Failed to parse record: %v", err)
	}

	// Check that datetime feature was converted to int64
	timestampValue, exists := result.Features["timestamp"]
	if !exists {
		t.Fatal("timestamp feature not found in result")
	}

	intValue, ok := timestampValue.(int64)
	if !ok {
		t.Errorf("Expected timestamp to be int64, got %T: %v", timestampValue, timestampValue)
	}

	expectedInt := int64(20231225143045)
	if intValue != expectedInt {
		t.Errorf("Expected timestamp %d, got %d", expectedInt, intValue)
	}

	// Check that other features are parsed correctly
	eventType, exists := result.Features["event_type"]
	if !exists {
		t.Fatal("event_type feature not found in result")
	}
	if eventType != "login" {
		t.Errorf("Expected event_type 'login', got %v", eventType)
	}

	value, exists := result.Features["value"]
	if !exists {
		t.Fatal("value feature not found in result")
	}
	if value != 42.5 {
		t.Errorf("Expected value 42.5, got %v", value)
	}
}

// TestParsePredictionRecord_DateTimeConversionError tests error handling for invalid datetime strings
func TestParsePredictionRecord_DateTimeConversionError(t *testing.T) {
	headers := []string{"timestamp", "event_type"}
	record := []string{"invalid-datetime", "login"}

	expectedFeatures := map[string]models.FeatureType{
		"timestamp":  models.DateTimeFeature,
		"event_type": models.CategoricalFeature,
	}

	_, err := parsePredictionRecord(record, headers, expectedFeatures, 1)
	if err == nil {
		t.Fatal("Expected error for invalid datetime string")
	}

	expectedErrorSubstring := "failed to parse datetime value"
	if !contains(err.Error(), expectedErrorSubstring) {
		t.Errorf("Expected error containing '%s', got: %v", expectedErrorSubstring, err)
	}
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			func() bool {
				for i := 0; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())))
}
