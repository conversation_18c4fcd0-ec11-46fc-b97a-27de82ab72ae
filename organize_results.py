#!/usr/bin/env python3
"""
Organize and compare results from main-v2 and main branch implementations.

This script creates a systematic comparison of the two implementations,
organizing files and generating comprehensive analysis.
"""

import os
import json
import shutil
from typing import Dict, List, Any
import csv


def load_results(results_file: str) -> List[Dict[str, Any]]:
    """Load results from JSON file."""
    if not os.path.exists(results_file):
        print(f"Warning: {results_file} not found")
        return []
    
    with open(results_file, 'r') as f:
        return json.load(f)


def organize_files():
    """Organize all result files into a structured directory."""
    # Create organized structure
    base_dir = "comparison_results"
    os.makedirs(base_dir, exist_ok=True)
    
    # Create subdirectories
    dirs = {
        'main_v2': os.path.join(base_dir, 'main_v2'),
        'main': os.path.join(base_dir, 'main'),
        'yaml_files': os.path.join(base_dir, 'yaml_files'),
        'analysis': os.path.join(base_dir, 'analysis')
    }
    
    for dir_path in dirs.values():
        os.makedirs(dir_path, exist_ok=True)
    
    # Copy main-v2 results
    if os.path.exists('results_mainv2'):
        for file in os.listdir('results_mainv2'):
            src = os.path.join('results_mainv2', file)
            dst = os.path.join(dirs['main_v2'], file)
            shutil.copy2(src, dst)
    
    # Copy main results
    if os.path.exists('results_main'):
        for file in os.listdir('results_main'):
            src = os.path.join('results_main', file)
            dst = os.path.join(dirs['main'], file)
            shutil.copy2(src, dst)
    
    # Copy YAML files
    yaml_dirs = {
        'main_v2_yaml': 'benchmark/data_mainv2',
        'main_yaml': 'benchmark/data'
    }
    
    for subdir, source_dir in yaml_dirs.items():
        target_dir = os.path.join(dirs['yaml_files'], subdir)
        os.makedirs(target_dir, exist_ok=True)
        
        if os.path.exists(source_dir):
            for file in os.listdir(source_dir):
                if file.endswith('.yaml'):
                    src = os.path.join(source_dir, file)
                    dst = os.path.join(target_dir, file)
                    shutil.copy2(src, dst)
    
    print(f"Files organized in: {base_dir}")
    return dirs


def compare_implementations(main_v2_results: List[Dict], main_results: List[Dict]) -> Dict[str, Any]:
    """Compare the two implementations across all metrics."""
    comparison = {
        'summary': {},
        'detailed': {},
        'datasets': {}
    }
    
    # Create dataset lookup for main results
    main_lookup = {r['dataset']: r for r in main_results if r.get('status') == 'success'}
    
    # Compare each dataset
    for mv2_result in main_v2_results:
        if mv2_result.get('status') != 'success':
            continue
            
        dataset = mv2_result['dataset']
        if dataset not in main_lookup:
            continue
            
        main_result = main_lookup[dataset]
        
        # Calculate differences
        dataset_comparison = {
            'dataset': dataset,
            'main_v2': {
                'training_time': mv2_result.get('training_time', 0),
                'total_nodes': mv2_result.get('total_nodes', 0),
                'leaf_nodes': mv2_result.get('leaf_nodes', 0),
                'max_depth': mv2_result.get('max_depth', 0),
                'algorithm': mv2_result.get('algorithm', 'Unknown')
            },
            'main': {
                'training_time': main_result.get('training_time', 0),
                'total_nodes': main_result.get('total_nodes', 0),
                'leaf_nodes': main_result.get('leaf_nodes', 0),
                'max_depth': main_result.get('max_depth', 0),
                'algorithm': main_result.get('algorithm', 'C4.5')
            },
            'differences': {}
        }
        
        # Calculate differences
        mv2_time = mv2_result.get('training_time', 0)
        main_time = main_result.get('training_time', 0)
        
        mv2_nodes = mv2_result.get('total_nodes', 0)
        main_nodes = main_result.get('total_nodes', 0)
        
        mv2_leaves = mv2_result.get('leaf_nodes', 0)
        main_leaves = main_result.get('leaf_nodes', 0)
        
        dataset_comparison['differences'] = {
            'training_time_diff': mv2_time - main_time,
            'training_time_ratio': mv2_time / main_time if main_time > 0 else float('inf'),
            'nodes_diff': mv2_nodes - main_nodes,
            'nodes_ratio': mv2_nodes / main_nodes if main_nodes > 0 else float('inf'),
            'leaves_diff': mv2_leaves - main_leaves,
            'complexity_comparison': 'main-v2 more complex' if mv2_nodes > main_nodes else 'main more complex' if main_nodes > mv2_nodes else 'similar complexity'
        }
        
        comparison['datasets'][dataset] = dataset_comparison
    
    # Calculate summary statistics
    datasets = list(comparison['datasets'].values())
    if datasets:
        avg_time_diff = sum(d['differences']['training_time_diff'] for d in datasets) / len(datasets)
        avg_nodes_diff = sum(d['differences']['nodes_diff'] for d in datasets) / len(datasets)
        
        comparison['summary'] = {
            'total_datasets': len(datasets),
            'avg_training_time_diff': avg_time_diff,
            'avg_nodes_diff': avg_nodes_diff,
            'main_v2_faster_count': sum(1 for d in datasets if d['differences']['training_time_diff'] < 0),
            'main_v2_simpler_count': sum(1 for d in datasets if d['differences']['nodes_diff'] < 0),
            'performance_winner': 'main-v2' if avg_time_diff < 0 else 'main',
            'complexity_winner': 'main-v2 (simpler)' if avg_nodes_diff < 0 else 'main (simpler)'
        }
    
    return comparison


def create_comparison_report(comparison: Dict[str, Any], output_dir: str):
    """Create detailed comparison report."""
    report_file = os.path.join(output_dir, 'comparison_report.txt')
    
    with open(report_file, 'w') as f:
        f.write("MULBERRI IMPLEMENTATION COMPARISON REPORT\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("EXECUTIVE SUMMARY\n")
        f.write("-" * 30 + "\n")
        summary = comparison['summary']
        f.write(f"Total datasets compared: {summary.get('total_datasets', 0)}\n")
        f.write(f"Average training time difference: {summary.get('avg_training_time_diff', 0):.3f}s\n")
        f.write(f"Average tree complexity difference: {summary.get('avg_nodes_diff', 0):.1f} nodes\n")
        f.write(f"Performance winner: {summary.get('performance_winner', 'N/A')}\n")
        f.write(f"Complexity winner: {summary.get('complexity_winner', 'N/A')}\n")
        f.write(f"Main-v2 faster on: {summary.get('main_v2_faster_count', 0)} datasets\n")
        f.write(f"Main-v2 simpler on: {summary.get('main_v2_simpler_count', 0)} datasets\n\n")
        
        f.write("DETAILED DATASET COMPARISON\n")
        f.write("-" * 40 + "\n")
        
        for dataset, data in comparison['datasets'].items():
            f.write(f"\nDataset: {dataset}\n")
            f.write(f"  Main-v2: {data['main_v2']['total_nodes']} nodes, {data['main_v2']['training_time']:.3f}s\n")
            f.write(f"  Main:    {data['main']['total_nodes']} nodes, {data['main']['training_time']:.3f}s\n")
            f.write(f"  Difference: {data['differences']['nodes_diff']:+d} nodes, {data['differences']['training_time_diff']:+.3f}s\n")
            f.write(f"  Complexity: {data['differences']['complexity_comparison']}\n")
    
    print(f"Comparison report saved to: {report_file}")


def create_csv_summary(comparison: Dict[str, Any], output_dir: str):
    """Create CSV summary for easy analysis."""
    csv_file = os.path.join(output_dir, 'comparison_summary.csv')
    
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        
        # Header
        writer.writerow([
            'Dataset', 'MainV2_Nodes', 'Main_Nodes', 'MainV2_Leaves', 'Main_Leaves',
            'MainV2_Depth', 'Main_Depth', 'MainV2_Time', 'Main_Time',
            'Nodes_Diff', 'Time_Diff', 'Complexity_Winner'
        ])
        
        # Data rows
        for dataset, data in comparison['datasets'].items():
            writer.writerow([
                dataset,
                data['main_v2']['total_nodes'],
                data['main']['total_nodes'],
                data['main_v2']['leaf_nodes'],
                data['main']['leaf_nodes'],
                data['main_v2']['max_depth'],
                data['main']['max_depth'],
                f"{data['main_v2']['training_time']:.3f}",
                f"{data['main']['training_time']:.3f}",
                data['differences']['nodes_diff'],
                f"{data['differences']['training_time_diff']:.3f}",
                'main-v2' if data['differences']['nodes_diff'] < 0 else 'main'
            ])
    
    print(f"CSV summary saved to: {csv_file}")


def main():
    """Main function to organize and compare results."""
    print("Organizing comparison results...")
    
    # Organize files
    dirs = organize_files()
    
    # Load results
    main_v2_results = load_results('results_mainv2/mainv2_training_results.json')
    main_results = load_results('results_main/main_training_results.json')
    
    print(f"Loaded {len(main_v2_results)} main-v2 results")
    print(f"Loaded {len(main_results)} main results")
    
    # Compare implementations
    comparison = compare_implementations(main_v2_results, main_results)
    
    # Save comparison data
    comparison_file = os.path.join(dirs['analysis'], 'detailed_comparison.json')
    with open(comparison_file, 'w') as f:
        json.dump(comparison, f, indent=2)
    
    # Create reports
    create_comparison_report(comparison, dirs['analysis'])
    create_csv_summary(comparison, dirs['analysis'])
    
    print("\n" + "=" * 60)
    print("COMPARISON COMPLETE")
    print("=" * 60)
    print(f"Results organized in: comparison_results/")
    print(f"Analysis files in: {dirs['analysis']}")
    
    # Print quick summary
    summary = comparison['summary']
    print(f"\nQuick Summary:")
    print(f"- Compared {summary.get('total_datasets', 0)} datasets")
    print(f"- Performance winner: {summary.get('performance_winner', 'N/A')}")
    print(f"- Complexity winner: {summary.get('complexity_winner', 'N/A')}")


if __name__ == "__main__":
    main()
