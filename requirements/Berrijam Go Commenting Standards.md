# Berrijam Go Commenting Standards

Version 0.2  
Last Updated: Aug 3, 2025

---

[Why](#why)

[Doc Fundamentals](#doc-fundamentals)

[Function Documentation](#function-documentation)

[Struct Documentation](#struct-documentation)

[Interface Documentation](#interface-documentation)

[Constants & Variables](#constants-&-variables)

[Error Types](#error-types)

[Package Documentation](#package-documentation)

[Optional Tags](#optional-tags)

[File or Data Formats](#file-or-data-formats)

---

# Why {#why}

The goal of documentation should be to provide sufficient detail about the function, interface, module or structure for someone to use it to understand its function and how it might use it. In future we will write a custom code parser to build a custom documentation extractor to succinctly identify the dependency maps. 

Our approach will be to use keywords within the comments to provide structure and detail while feeling natural in Go's ecosystem. It also makes it easier for code file parsers to extract relevant information, and ensure \`go doc\` output remains clean and readable

# Doc Fundamentals {#doc-fundamentals}

1. **Purpose** \- What this does  
2. **Constraints** \- Validation rules, ranges, required/optional  
3. **Security** \- Sensitive data, access controls  
4. **Examples** \- For complex APIs  
5. **Relationships** \- How components interact  
6. **Side effects** \- What changes when modified

# Function Documentation {#function-documentation}

```go
// ProcessPayment handles subscription payment processing.
//
// Args:
//   - userID: Customer ID (must be > 0)
//   - amount: Payment in cents (1-999999999)
//   - paymentMethod: Stripe payment method (format: pm_xxxxx)
//   - metadata: Optional tracking data (max 50 entries)
//
// Returns transaction details or payment/network errors.
// Retries network failures up to 3x. Logs all attempts for compliance.
//
// Example: result, err := ProcessPayment(123, 2999, "pm_abc123", nil)
func ProcessPayment(userID int64, amount int, paymentMethod string, metadata map[string]string) (*PaymentResult, error)
```

# Struct Documentation {#struct-documentation}

```go
// User represents a registered system user.
//
// Security: HashedPassword contains bcrypt hash, Email must be unique.
// Relationships: SubscriptionID links to subscription, nil = free tier.
type User struct {
    // ID is unique user identifier (auto-generated, immutable)
    ID int64 `json:"id" db:"id"`
    
    // Email for login and communication (unique, validated format required)
    Email string `json:"email" db:"email" validate:"required,email"`
    
    // HashedPassword stores bcrypt hash (never plaintext)
    HashedPassword string `json:"-" db:"hashed_password"`
    
    // Profile contains public user info (nil if incomplete setup)
    Profile *UserProfile `json:"profile,omitempty"`
    
    // SubscriptionID references active subscription (nil = free tier)
    SubscriptionID *int64 `json:"subscription_id,omitempty" db:"subscription_id"`
    
    // IsActive enables/disables account (false = suspended)
    IsActive bool `json:"is_active" db:"is_active"`
}
```

# Interface Documentation {#interface-documentation}

```go
// PaymentProcessor abstracts payment gateway implementations.
//
// All methods should be idempotent and handle timeouts gracefully.
// Error types must be consistent across implementations.
type PaymentProcessor interface {
    // ProcessPayment charges amount using payment method
    ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResult, error)
    
    // RefundPayment reverses transaction (supports partial refunds)
    RefundPayment(ctx context.Context, transactionID string, amount int) (*RefundResult, error)
    
    // ValidatePaymentMethod checks validity without charging
    ValidatePaymentMethod(ctx context.Context, paymentMethodID string) error
}
```

# Constants & Variables {#constants-&-variables}

```go
const (
    MaxRetryAttempts = 5    // Prevents infinite retry loops
    DefaultTimeout = 30     // HTTP timeout balancing speed/reliability
    WebhookTolerance = 300  // Clock skew allowance (Stripe max recommended)
)

var (
    // ErrPaymentDeclined: card declined (insufficient funds, expired, fraud)
    ErrPaymentDeclined = errors.New("payment declined by issuer")
    
    // ErrInvalidAmount: amount outside range ($0.50-$999,999)
    ErrInvalidAmount = errors.New("payment amount invalid")
)
```

# Error Types {#error-types}

```go
// PaymentError provides structured payment error information.
//
// Codes: card_declined, insufficient_funds, expired_card, network_error, rate_limited
// Retryable: network_error, rate_limited only
// UserMessage: safe for end-user display
type PaymentError struct {
    Code        string `json:"code"`         // Machine-readable
    UserMessage string `json:"user_message"` // End-user safe
    Details     string `json:"-"`            // Internal only
    Retryable   bool   `json:"retryable"`    // Retry guidance
    Cause       error  `json:"-"`            // Underlying error
}
```

# Package Documentation {#package-documentation}

```go
// Package payments handles payment processing for Berrijam platform.
//
// Architecture:
//   - processor.go: Core payment logic
//   - webhooks.go: Stripe webhook handling  
//   - subscriptions.go: Recurring payments
//   - models.go: Data structures
//
// Configuration (environment variables):
//   - STRIPE_API_KEY: Stripe secret key
//   - STRIPE_WEBHOOK_SECRET: Webhook secret
//   - PAYMENT_TIMEOUT: Request timeout (default: 30s)
//
// Example:
//   processor := payments.NewProcessor(config)
//   result, err := processor.ProcessPayment(ctx, request)
//
// Security: PCI DSS compliance required. Validate webhook signatures.
// Dependencies: Stripe Go SDK v72+, database connection
package payments
```

# Optional Tags {#optional-tags}

Use when relevant:

* `// Security:` \- PCI compliance, sensitive data handling  
* `// Performance:` \- Complexity, caching behavior  
* `// Deprecated:` \- Replacement guidance, removal timeline

# File or Data Formats {#file-or-data-formats}

In certain situations we might need to define a custom file or data format. This documentation should be documented within [README.md](http://README.md) at the package or interface level. 

‼️**Never check in large binary files into the source code**. Only define the standards in human readable formats and use small sample files within the code base. Large data files should only be stored within Google Drive folders or Berrijam’s Object Store.

