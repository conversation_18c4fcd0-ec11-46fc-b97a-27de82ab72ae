# Mulberri: Lets Get the Basics Right

---

[Overview](#overview)

[FeatureColumn](#featurecolumn)

[Dataset and DatasetView](#dataset-and-datasetview)

[SplitEvaluator, NumericalSplitEvaluator and CategoricalSplitEvaluator](#splitevaluator,-numericalsplitevaluator-and-categoricalsplitevaluator)

[Creating DatasetView based on Splits](#creating-datasetview-based-on-splits)

[Walkthrough Example](#walkthrough-example)

[Apply Split Evaluation](#apply-split-evaluation)

[FeatureType, SplitType, FeatureInfo, sorting… etc](#featuretype,-splittype,-featureinfo,-sorting…-etc)

[Node & SerializableNode](#node-&-serializablenode)

[Decision Node](#decision-node)

[Example Numeric Decision Split](#example-numeric-decision-split)

[Example Nominal Decision Split](#example-nominal-decision-split)

[Node Serialization to JSON](#node-serialization-to-json)

[DecisionTree & TreeMetadata](#decisiontree-&-treemetadata)

[Tree Serialization and De-Serialization](#tree-serialization-and-de-serialization)

[Simplify Config and Parameters](#simplify-config-and-parameters)

[Handle Missing Values](#handle-missing-values)

[Feature Info YAML](#feature-info-yaml)

[Sample JSON Tree File](#sample-json-tree-file)

[Code Structure for Clarity](#code-structure-for-clarity)

---

## Overview {#overview}

Our current implementation is overly complex and requires a lot of refactoring. 

First thing we are going to do is design the right underlying data structures for clarity, simplicity and efficiency. 

Here are some guiding principles for this re-design:

1. Support int, float and str types only. \- Our loading \+ parsing will convert all datasets into corresponding 3 types only  
2. One copy of data only in memory \- Only keep one copy of dataset in memory and use views to reference subset of rows at each node avoiding unnecessary copies  
3. Clean interface and only focus on C4.5 splitting based on information gain. We don’t need other algorithms at the moment, although it creates the possibility to introduce new algorithms in the future.  
4. Support binary classification only \- We do not need numeric target values at this point.   
5. Parallelism will come later \- There might be two ways to parallelize \- parallel feature split at a given node or parallel branch creation. We don’t know which would be better right now. So first is to ensure we have the iterative, single threaded version working correctly first to get a benchmark/baseline to add optimizations against in the future.

We will snapshot and create a brand new branch and start fresh. Although we have a lot of code as reference, we will not be copying it across wholesale. We will only bring in pieces of functionality as relevant.

## FeatureColumn {#featurecolumn}

The first thing is starting with the right data structures and interfaces. Selecting the right data structures can dramatically simplify our code/logic. 

We will define a **FeatureColumn** interface first, with implementations for int, float and str. We will have IntColumn, FloatColumn and StringColumn that implement that FeatureColumn interface

```go
// ====================
// Feature Column Interface and Implementations
// ====================

// FeatureColumn abstracts access to different column types
type FeatureColumn interface {
    GetValue(physicalIndex int) (interface{}, error)
    GetNumericalValue(physicalIndex int) (float64, error) // for numerical splits
    GetSize() int
    GetType() FeatureType
    IsNumerical() bool
}

// IntColumn stores integer feature data
type IntColumn struct {
    data     []int64
    nullMask []bool
}

func (c *IntColumn) GetValue(index int) (interface{}, error) {
    if index >= len(c.data) {
        return nil, fmt.Errorf("index out of bounds")
    }
    if c.nullMask[index] {
        return nil, fmt.Errorf("missing value")
    }
    return c.data[index], nil
}

func (c *IntColumn) GetNumericalValue(index int) (float64, error) {
    if index >= len(c.data) {
        return 0, fmt.Errorf("index out of bounds")
    }
    if c.nullMask[index] {
        return 0, fmt.Errorf("missing value")
    }
    return float64(c.data[index]), nil
}

func (c *IntColumn) GetSize() int        { return len(c.data) }
func (c *IntColumn) GetType() FeatureType { return IntegerFeature }
func (c *IntColumn) IsNumerical() bool   { return true }

// FloatColumn stores float feature data
type FloatColumn struct {
    data     []float64
    nullMask []bool
}

func (c *FloatColumn) GetValue(index int) (interface{}, error) {
    if index >= len(c.data) {
        return nil, fmt.Errorf("index out of bounds")
    }
    if c.nullMask[index] {
        return nil, fmt.Errorf("missing value")
    }
    return c.data[index], nil
}

func (c *FloatColumn) GetNumericalValue(index int) (float64, error) {
    if index >= len(c.data) {
        return 0, fmt.Errorf("index out of bounds")
    }
    if c.nullMask[index] {
        return 0, fmt.Errorf("missing value")
    }
    return c.data[index], nil
}

func (c *FloatColumn) GetSize() int        { return len(c.data) }
func (c *FloatColumn) GetType() FeatureType { return FloatFeature }
func (c *FloatColumn) IsNumerical() bool   { return true }

// StringColumn stores string feature data
type StringColumn struct {
    data     []string
    nullMask []bool
}

func (c *StringColumn) GetValue(index int) (interface{}, error) {
    if index >= len(c.data) {
        return nil, fmt.Errorf("index out of bounds")
    }
    if c.nullMask[index] {
        return nil, fmt.Errorf("missing value")
    }
    return c.data[index], nil
}

func (c *StringColumn) GetNumericalValue(index int) (float64, error) {
    return 0, fmt.Errorf("string column cannot be converted to numerical value")
}

func (c *StringColumn) GetSize() int        { return len(c.data) }
func (c *StringColumn) GetType() FeatureType { return StringFeature }
func (c *StringColumn) IsNumerical() bool   { return false }
```

## Dataset and DatasetView {#dataset-and-datasetview}

Next we will define **Dataset** and **DatasetView** data structures. This makes it very clear when we are working with a view or subset of rows referenced in the full dataset, depending on which node of the tree we are in. Also we will store data by column (makes for fast scanning or contiguous values) and in explicitly referenced types of data..

```go
// ====================
// Dataset Structure (Full Data Storage)
// ====================

type Dataset[T comparable] struct {
    // Separate storage for each type - FULL DATA
    intColumns    map[string]*IntColumn    // "age", "experience_years"
    floatColumns  map[string]*FloatColumn  // "salary", "gpa", "height"
    stringColumns map[string]*StringColumn // "education", "department", "city"
    
    targets       []T                      // target values - FULL ARRAY
    featureInfo   map[string]*FeatureInfo  // metadata
    featureOrder  []string                 // consistent feature ordering
    totalSize     int                      // total dataset size
}
```

When we are loading a training or prediction dataset, we will generate only a single Dataset instance. 

```go
// DatasetView represents a subset of the full dataset
type DatasetView[T comparable] struct {
    dataset       *Dataset[T]  // Reference to full dataset
    activeIndices []int        // Which rows from full dataset are active
    size         int           // Number of active rows
    
    // Cached calculations for this view
    targetDist    map[T]int    // Cached target distribution
    targetDistDirty bool       // Whether cache needs refresh
}
```

Notice that we are keeping a cache of calculations for a view, which may be required multiple times, and can be invalidated (i.e. set targetDistDirty= **true**) whenever we create a few ‘view’ and then set to **false** when the calculation is done. It can also be explicitly changed but given we are not changing underlying dataset.

We create a DatasetView (i.e. subset of rows/samples) by supplying the row indices

```go
// ====================
// Dataset Methods (Full Data Operations)
// ====================

// CreateView creates a view with specified row indices
func (d *Dataset[T]) CreateView(activeIndices []int) *DatasetView[T] {
    return &DatasetView[T]{
        dataset:         d,
        activeIndices:   activeIndices,
        size:           len(activeIndices),
        targetDistDirty: true, // needs calculation
    }
}

// GetColumn returns column interface for any feature type
func (d *Dataset[T]) GetColumn(featureName string) (FeatureColumn, error) {
    // Check in all three type maps
    if col, exists := d.intColumns[featureName]; exists {
        return col, nil
    }
    if col, exists := d.floatColumns[featureName]; exists {
        return col, nil
    }
    if col, exists := d.stringColumns[featureName]; exists {
        return col, nil
    }
    return nil, fmt.Errorf("feature %s not found", featureName)
}
```

On DatasetViews, can then operate on the subset of rows, to get values, calculate distribution, or create child views. Subset of rows are defined by **‘activeIndices**’

```go
// ====================
// DatasetView Methods (Subset Operations)  
// ====================

// GetFeatureValue gets value for logical index within this view
func (v *DatasetView[T]) GetFeatureValue(logicalIndex int, featureName string) (interface{}, error) {
    if logicalIndex >= v.size {
        return nil, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
    }
    
    // Convert logical index to physical index in full dataset
    physicalIndex := v.activeIndices[logicalIndex]
    
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, err
    }
    
    return column.GetValue(physicalIndex)
}

// GetTarget gets target for logical index within this view
func (v *DatasetView[T]) GetTarget(logicalIndex int) (T, error) {
    if logicalIndex >= v.size {
        var zero T
        return zero, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
    }
    
    physicalIndex := v.activeIndices[logicalIndex]
    return v.dataset.targets[physicalIndex], nil
}

// GetTargetDistribution calculates distribution for active indices only
func (v *DatasetView[T]) GetTargetDistribution() (map[T]int, error) {
    if !v.targetDistDirty && v.targetDist != nil {
        return v.targetDist, nil // return cached
    }
    
    distribution := make(map[T]int)
    
    // Only iterate over active indices
    for _, physicalIndex := range v.activeIndices {
        target := v.dataset.targets[physicalIndex]
        distribution[target]++
    }
    
    v.targetDist = distribution
    v.targetDistDirty = false
    return distribution, nil
}

// CreateChildView creates a new view from subset of current view's indices
func (v *DatasetView[T]) CreateChildView(logicalIndices []int) *DatasetView[T] {
    // Convert logical indices (within this view) to physical indices (in full dataset)
    physicalIndices := make([]int, len(logicalIndices))
    for i, logicalIndex := range logicalIndices {
        physicalIndices[i] = v.activeIndices[logicalIndex]
    }
    
    return &DatasetView[T]{
        dataset:         v.dataset, // Same full dataset reference
        activeIndices:   physicalIndices,
        size:           len(physicalIndices),
        targetDistDirty: true,
    }
}
```

## SplitEvaluator, NumericalSplitEvaluator and CategoricalSplitEvaluator {#splitevaluator,-numericalsplitevaluator-and-categoricalsplitevaluator}

Next we implement the Split Evaluation by providing an interface that handles each type accordingly. For int/float we handle **NumericalSplitEvaluator** and for string we use **CategoricalSplitEvaluator**. We could use **StringTypeSplitEvaluator** rather than CategoricalSplitEvaluator, to keep things consistent and not confuse with the dataset type. 

```go
// ====================
// Split Evaluation Strategy Pattern
// ====================

// SplitEvaluator handles different splitting algorithms
type SplitEvaluator[T comparable] interface {
    EvaluateSplits(view *DatasetView[T], column FeatureColumn, baseImpurity float64) ([]SplitCandidate, error)
}

// NumericalSplitEvaluator handles BOTH int and float features
type NumericalSplitEvaluator[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (n *NumericalSplitEvaluator[T]) EvaluateSplits(
    view *DatasetView[T],      // OPERATES ON VIEW, NOT FULL DATASET
    column FeatureColumn, 
    baseImpurity float64,
) ([]SplitCandidate, error) {
    
    // Get sorted pairs from ONLY the active indices in the view
    sortedPairs, err := n.getSortedNumericalPairs(view, column)
    if err != nil {
        return nil, err
    }
    
    var candidates []SplitCandidate
    
    // Initialize target distributions for the VIEW's data only
    leftDist := make(map[T]int)
    rightDist := make(map[T]int)
    
    // All samples from VIEW start in right distribution
    for _, pair := range sortedPairs {
        rightDist[pair.target]++
    }
    
    // Sweep through sorted values (only active indices)
    for i := 0; i < len(sortedPairs)-1; i++ {
        currentPair := sortedPairs[i]
        
        // Move sample from right to left
        leftDist[currentPair.target]++
        rightDist[currentPair.target]--
        if rightDist[currentPair.target] == 0 {
            delete(rightDist, currentPair.target)
        }
        
        // Create split candidate if values are different
        if sortedPairs[i].value != sortedPairs[i+1].value {
            threshold := (sortedPairs[i].value + sortedPairs[i+1].value) / 2
            
            leftSize := i + 1
            rightSize := len(sortedPairs) - leftSize
            
            leftImpurity := n.impurityCalc.Calculate(leftDist, leftSize)
            rightImpurity := n.impurityCalc.Calculate(rightDist, rightSize)
            
            weightedImpurity := (float64(leftSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(view.size)
            gain := baseImpurity - weightedImpurity
            
            candidates = append(candidates, SplitCandidate{
                Type:      NumericalSplit,
                Threshold: &threshold,
                Gain:      gain,
                LeftSize:  leftSize,
                RightSize: rightSize,
            })
        }
    }
    
    return candidates, nil
}

// getSortedNumericalPairs works with VIEW's active indices only
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(view *DatasetView[T], column FeatureColumn) ([]numericalPair[T], error) {
    pairs := make([]numericalPair[T], view.size) // Only VIEW's size
    
    // Iterate through VIEW's active indices only
    for i := 0; i < view.size; i++ {
        physicalIndex := view.activeIndices[i] // Convert to physical index
        
        // Get numerical value from full dataset using physical index
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        // Get target from view using logical index
        target, err := view.GetTarget(i)
        if err != nil {
            return nil, err
        }
        
        pairs[i] = numericalPair[T]{value: numValue, target: target}
    }
    
    // Sort by numerical value
    sort.Slice(pairs, func(i, j int) bool {
        return pairs[i].value < pairs[j].value
    })
    
    return pairs, nil
}
```

Similarly for CategoricalSplitEvaluator … 

```go
// CategoricalSplitEvaluator handles string features using VIEW data
type CategoricalSplitEvaluator[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (c *CategoricalSplitEvaluator[T]) EvaluateSplits(
    view *DatasetView[T],      // OPERATES ON VIEW, NOT FULL DATASET
    column FeatureColumn, 
    baseImpurity float64,
) ([]SplitCandidate, error) {
    
    // Get joint distribution from VIEW's active indices only
    jointDist, err := c.getStringJointDistribution(view, column)
    if err != nil {
        return nil, err
    }
    
    var candidates []SplitCandidate
    
    // For each unique string value in the VIEW, create a binary split candidate
    for stringValue, targetCounts := range jointDist {
        subsetSize := 0
        for _, count := range targetCounts {
            subsetSize += count
        }
        
        if subsetSize == 0 || subsetSize == view.size {
            continue // Skip pure splits
        }
        
        // Calculate split quality: "education == 'college'" vs "education != 'college'"
        leftImpurity := c.impurityCalc.Calculate(targetCounts, subsetSize)
        
        // Calculate right side (all other values in this VIEW)
        rightDist := c.calculateComplementDistribution(view, jointDist, stringValue)
        rightSize := view.size - subsetSize
        rightImpurity := c.impurityCalc.Calculate(rightDist, rightSize)
        
        weightedImpurity := (float64(subsetSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(view.size)
        gain := baseImpurity - weightedImpurity
        
        candidates = append(candidates, SplitCandidate{
            Type:      CategoricalSplit,
            Value:     stringValue,
            Gain:      gain,
            LeftSize:  subsetSize,
            RightSize: rightSize,
        })
    }
    
    return candidates, nil
}

// getStringJointDistribution calculates joint distribution for VIEW's active indices
func (c *CategoricalSplitEvaluator[T]) getStringJointDistribution(view *DatasetView[T], column FeatureColumn) (map[interface{}]map[T]int, error) {
    jointDist := make(map[interface{}]map[T]int)
    
    // Iterate through VIEW's active indices only
    for logicalIndex := 0; logicalIndex < view.size; logicalIndex++ {
        physicalIndex := view.activeIndices[logicalIndex]
        
        // Get feature value using physical index
        featureValue, err := column.GetValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        // Get target using logical index within view
        target, err := view.GetTarget(logicalIndex)
        if err != nil {
            return nil, err
        }
        
        if jointDist[featureValue] == nil {
            jointDist[featureValue] = make(map[T]int)
        }
        jointDist[featureValue][target]++
    }
    
    return jointDist, nil
}

// calculateComplementDistribution calculates target distribution for all values != splitValue
func (c *CategoricalSplitEvaluator[T]) calculateComplementDistribution(view *DatasetView[T], jointDist map[interface{}]map[T]int, splitValue interface{}) map[T]int {
    rightDist := make(map[T]int)
    
    for featureValue, targetCounts := range jointDist {
        if featureValue != splitValue {
            for target, count := range targetCounts {
                rightDist[target] += count
            }
        }
    }
    
    return rightDist
}
```

Next we’ll have a Factory to return the correct type

```go
// ====================
// Factory Pattern for All Three Types
// ====================

type SplitEvaluatorFactory[T comparable] struct {
    impurityCalc ImpurityCalculator[T]
}

func (f *SplitEvaluatorFactory[T]) CreateEvaluator(column FeatureColumn) SplitEvaluator[T] {
    switch column.GetType() {
    case IntegerFeature, FloatFeature:
        // BOTH int and float use numerical splitting
        return &NumericalSplitEvaluator[T]{impurityCalc: f.impurityCalc}
    case StringFeature:
        return &CategoricalSplitEvaluator[T]{impurityCalc: f.impurityCalc}
    default:
        return nil // or error
    }
}

// ====================
// Split Application for All Types
// ====================

// SplitCandidate represents potential splits for any feature type
type SplitCandidate struct {
    FeatureName string
    Type        SplitType
    
    // For numerical splits (int/float)
    Threshold   *float64     // "age <= 30.5" or "salary <= 65000.0"
    
    // For categorical splits (string)
    Value       interface{}  // "education == 'college'"
    
    Gain        float64
    LeftSize    int
    RightSize   int
}

// Apply split to create child dataset views
func (sc *SplitCandidate) ApplySplit(view *DatasetView[T]) (*DatasetView[T], *DatasetView[T], error) {
    switch sc.Type {
    case NumericalSplit:
        // Works for BOTH int and float columns
        return view.SplitByNumericalThreshold(sc.FeatureName, *sc.Threshold)
    case CategoricalSplit:
        // Works for string columns
        return view.SplitByStringValue(sc.FeatureName, sc.Value)
    default:
        return nil, nil, fmt.Errorf("unknown split type")
    }
}
```

## Creating DatasetView based on Splits {#creating-datasetview-based-on-splits}

Now lets look at how we create DatasetView splits based on the type of column. Numeric type should return two subset \- left and right.

```go
// ====================
// DatasetView Split Methods for All Types
// ====================

// SplitByNumericalThreshold works for BOTH int and float features
func (v *DatasetView[T]) SplitByNumericalThreshold(featureName string, threshold float64) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    if !column.IsNumerical() {
        return nil, nil, fmt.Errorf("feature %s is not numerical", featureName)
    }
    
    var leftIndices, rightIndices []int
    
    for _, physicalIndex := range v.activeIndices {
        // GetNumericalValue() works for both IntColumn and FloatColumn
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        if numValue <= threshold {
            leftIndices = append(leftIndices, physicalIndex)
        } else {
            rightIndices = append(rightIndices, physicalIndex)
        }
    }
    
    leftView := v.dataset.CreateView(leftIndices)
    rightView := v.dataset.CreateView(rightIndices)
    
    return leftView, rightView, nil
}


```

In this example we are doing the same for StringValues. But we should actually return multiple branches (i.e. more than 2 DatasetViews, one for each string-value within the set of values being split)

```go
// SplitByStringValue works for string features
func (v *DatasetView[T]) SplitByStringValue(featureName string, splitValue interface{}) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    if column.GetType() != StringFeature {
        return nil, nil, fmt.Errorf("feature %s is not string", featureName)
    }
    
    splitString := splitValue.(string)
    var leftIndices, rightIndices []int
    
    for _, physicalIndex := range v.activeIndices {
        value, err := column.GetValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        stringValue := value.(string)
        if stringValue == splitString {
            leftIndices = append(leftIndices, physicalIndex)  // "education == 'college'"
        } else {
            rightIndices = append(rightIndices, physicalIndex) // "education != 'college'"
        }
    }
    
    leftView := v.dataset.CreateView(leftIndices)
    rightView := v.dataset.CreateView(rightIndices)
    
    return leftView, rightView, nil
}
```

## Walkthrough Example {#walkthrough-example}

Now lets look at an example showing Dataset and DatasetView, with splitting

```go
// ====================
// Complete Usage Example Showing Dataset vs DatasetView
// ====================

func CompleteExampleWithViews() {
    // Step 1: Create full dataset with all three types
    dataset := &Dataset[string]{
        intColumns: map[string]*IntColumn{
            "age": {data: []int64{25, 30, 35, 40, 22, 45}},
        },
        floatColumns: map[string]*FloatColumn{
            "salary": {data: []float64{50000.5, 75000.0, 60000.5, 80000.0, 45000.0, 90000.0}},
        },
        stringColumns: map[string]*StringColumn{
            "education": {data: []string{"college", "high_school", "college", "graduate", "high_school", "graduate"}},
        },
        targets: []string{"yes", "no", "yes", "yes", "no", "yes"}, // 6 samples total
        totalSize: 6,
    }
    
    // Step 2: Create root view (all data active)
    rootView := dataset.CreateView([]int{0, 1, 2, 3, 4, 5}) // All 6 rows active
    
    // Step 3: Calculate base impurity for the VIEW
    targetDist, _ := rootView.GetTargetDistribution()
    // targetDist = {"yes": 4, "no": 2} from VIEW's active indices only
    baseImpurity := entropyCalculator.Calculate(targetDist, rootView.size) // Uses VIEW size (6)
    
    // Step 4: Evaluate splits on the VIEW
    factory := &SplitEvaluatorFactory[string]{impurityCalc: entropyCalculator}
    
    var bestCandidate *SplitCandidate
    
    // Evaluate "age" (integer feature)
    ageColumn, _ := dataset.GetColumn("age")
    ageEvaluator := factory.CreateEvaluator(ageColumn) // Returns NumericalSplitEvaluator
    ageCandidates, _ := ageEvaluator.EvaluateSplits(rootView, ageColumn, baseImpurity)
    // ageCandidates might include: {Threshold: 27.5, Gain: 0.23} for "age <= 27.5"
    
    // Evaluate "salary" (float feature)  
    salaryColumn, _ := dataset.GetColumn("salary")
    salaryEvaluator := factory.CreateEvaluator(salaryColumn) // Returns NumericalSplitEvaluator
    salaryCandidates, _ := salaryEvaluator.EvaluateSplits(rootView, salaryColumn, baseImpurity)
    // salaryCandidates might include: {Threshold: 67500.0, Gain: 0.31} for "salary <= 67500.0"
    
    // Evaluate "education" (string feature)
    educationColumn, _ := dataset.GetColumn("education")
    educationEvaluator := factory.CreateEvaluator(educationColumn) // Returns CategoricalSplitEvaluator
    educationCandidates, _ := educationEvaluator.EvaluateSplits(rootView, educationColumn, baseImpurity)
    // educationCandidates might include: {Value: "college", Gain: 0.45} for "education == 'college'"
    
    // Step 5: Find best split across all types
    allCandidates := append(append(ageCandidates, salaryCandidates...), educationCandidates...)
    for _, candidate := range allCandidates {
        if bestCandidate == nil || candidate.Gain > bestCandidate.Gain {
            bestCandidate = &candidate
        }
    }
    
    // Step 6: Apply best split to create child views
    leftChildView, rightChildView, _ := bestCandidate.ApplySplit(rootView)
    
    // Example results if best split was "education == 'college'":
    // leftChildView.activeIndices = [0, 2]     // Rows where education == "college"
    // rightChildView.activeIndices = [1, 3, 4, 5] // Rows where education != "college"
    //
    // Both child views reference the SAME full dataset
    // They just have different activeIndices arrays
    
    // Step 7: Recursive tree building - each child view can be split further
    // This is where the memory efficiency shines - no data copying!
    
    // For left child (college students), recalculate distributions:
    leftTargetDist, _ := leftChildView.GetTargetDistribution()
    // leftTargetDist = {"yes": 2, "no": 0} calculated from activeIndices [0, 2] only
    
    // For right child (non-college), recalculate distributions:
    rightTargetDist, _ := rightChildView.GetTargetDistribution()  
    // rightTargetDist = {"yes": 2, "no": 2} calculated from activeIndices [1, 3, 4, 5] only
}
```

And then we’d simply wrap it up recursively building up the tree, creating DatasetViews to push down each recursive call. 

## Apply Split Evaluation {#apply-split-evaluation}

During inference we’d do something like the following…

```go

// SplitCondition defines how to evaluate if a value goes left or right
type SplitCondition interface {
    GoesLeft(value interface{}) (bool, error)
}

// NumericalCondition for threshold-based splits
type NumericalCondition struct {
    threshold float64
}

func (n *NumericalCondition) GoesLeft(value interface{}) (bool, error) {
    // Convert to numerical value
    numValue, err := convertToFloat64(value)
    if err != nil {
        return false, err
    }
    return numValue <= n.threshold, nil
}

// CategoricalCondition for value-based splits
type CategoricalCondition struct {
    splitValue string
}

func (c *CategoricalCondition) GoesLeft(value interface{}) (bool, error) {
    stringValue, ok := value.(string)
    if !ok {
        return false, fmt.Errorf("expected string, got %T", value)
    }
    return stringValue == c.splitValue, nil
}

// Single unified split method
func (v *DatasetView[T]) SplitByCondition(featureName string, condition SplitCondition) (*DatasetView[T], *DatasetView[T], error) {
    column, err := v.dataset.GetColumn(featureName)
    if err != nil {
        return nil, nil, err
    }
    
    var leftPhysicalIndices, rightPhysicalIndices []int
    
    // Single loop for all feature types
    for _, physicalIndex := range v.activeIndices {
        value, err := column.GetValue(physicalIndex)
        if err != nil {
            continue // skip missing values
        }
        
        goesLeft, err := condition.GoesLeft(value)
        if err != nil {
            continue // skip invalid values
        }
        
        if goesLeft {
            leftPhysicalIndices = append(leftPhysicalIndices, physicalIndex)
        } else {
            rightPhysicalIndices = append(rightPhysicalIndices, physicalIndex)
        }
    }
    
    leftChildView := v.dataset.CreateView(leftPhysicalIndices)
    rightChildView := v.dataset.CreateView(rightPhysicalIndices)
    
    return leftChildView, rightChildView, nil
}


func (sc *SplitCandidate) ApplySplit(view *DatasetView[T]) (*DatasetView[T], *DatasetView[T], error) {
    var condition SplitCondition
    
    switch sc.Type {
    case NumericalSplit:
        condition = &NumericalCondition{threshold: *sc.Threshold}
    case CategoricalSplit:
        condition = &CategoricalCondition{splitValue: sc.Value.(string)}
    default:
        return nil, nil, fmt.Errorf("unknown split type")
    }
    
    return view.SplitByCondition(sc.FeatureName, condition)
}

```

## FeatureType, SplitType, FeatureInfo, sorting… etc {#featuretype,-splittype,-featureinfo,-sorting…-etc}

Other helper types that we’d need would then become

```go
// ====================
// Helper Types
// ====================

type FeatureType int
const (
    IntegerFeature FeatureType = iota
    FloatFeature
    StringFeature
)

type SplitType int
const (
    NumericalSplit SplitType = iota   // For int/float: uses threshold
    CategoricalSplit                  // For string: uses specific value
)

type FeatureInfo struct {
    Name         string
    Type         FeatureType
    UniqueValues []interface{} // cached for categorical features
}

// Helper for numerical evaluator
type numericalPair[T comparable] struct {
    value  float64  // converted to float64 (works for both int and float)
    target T
}

// Helper to sort numeric values 
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(view *DatasetView[T], column FeatureColumn) ([]numericalPair[T], error) {
    pairs := make([]numericalPair[T], view.size)
    
    for i, physicalIndex := range view.activeIndices {
        // GetNumericalValue() converts both int64->float64 and float64->float64
        numValue, err := column.GetNumericalValue(physicalIndex)
        if err != nil {
            return nil, err
        }
        
        target, err := view.GetTarget(i)
        if err != nil {
            return nil, err
        }
        
        pairs[i] = numericalPair[T]{value: numValue, target: target}
    }
    
    // Sort by numerical value (works for both int and float)
    sort.Slice(pairs, func(i, j int) bool {
        return pairs[i].value < pairs[j].value
    })
    
    return pairs, nil
}
```

## Node & SerializableNode {#node-&-serializablenode}

We will use Interfaces to define **Nodes** operations and structs for **LeafNode** and **DecisionNode**, that can be serialized to and from JSON.

```go
// Node represents any node in a decision tree
type Node interface {
	IsLeaf() bool
	GetSamples() int
	GetClassDistribution() map[interface{}]int
	GetMajorityClass() interface{}
	GetConfidence() float64
	Validate() error
	
	// Serialization support
	GetNodeType() string
	ToSerializable() *SerializableNode
}

// SerializableNode is the unified structure for JSON serialization
type SerializableNode struct {
	Type              string                       `json:"type"`                         // "leaf" or "decision"
	
	// Leaf node fields
	Prediction        interface{}                  `json:"prediction,omitempty"`
	
	// Decision node fields  
	Feature           *Feature                     `json:"feature,omitempty"`
	SplitValue        interface{}                  `json:"split_value,omitempty"`
	Children          map[string]*SerializableNode `json:"children,omitempty"`
	
	// Common fields
	ClassDistribution map[string]int               `json:"class_distribution"`
	Samples          int                          `json:"samples"`
	Confidence       float64                      `json:"confidence"`
}
```

LeafNode implements the interface and maintains its own fields

```go
// LeafNode represents a terminal node with prediction
type LeafNode struct {
	Prediction        interface{}         `json:"prediction"`
	ClassDistribution map[interface{}]int `json:"class_distribution"`
	Samples          int                 `json:"samples"`
	Confidence       float64             `json:"confidence"`
}

// NewLeafNode creates a leaf node with automatic confidence calculation
func NewLeafNode(classDistribution map[interface{}]int) (*LeafNode, error) {
	if len(classDistribution) == 0 {
		return nil, fmt.Errorf("leaf node requires non-empty class distribution")
	}
	
	// Calculate total samples and find majority class
	totalSamples := 0
	majorityClass := interface{}(nil)
	maxCount := 0
	
	for class, count := range classDistribution {
		if count < 0 {
			return nil, fmt.Errorf("class count cannot be negative: %d", count)
		}
		totalSamples += count
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}
	
	if totalSamples == 0 {
		return nil, fmt.Errorf("leaf node cannot have zero samples")
	}
	
	confidence := float64(maxCount) / float64(totalSamples)
	
	return &LeafNode{
		Prediction:        majorityClass,
		ClassDistribution: classDistribution,
		Samples:          totalSamples,
		Confidence:       confidence,
	}, nil
}

func (l *LeafNode) IsLeaf() bool                            { return true }
func (l *LeafNode) GetSamples() int                         { return l.Samples }
func (l *LeafNode) GetClassDistribution() map[interface{}]int { return l.ClassDistribution }
func (l *LeafNode) GetMajorityClass() interface{}           { return l.Prediction }
func (l *LeafNode) GetConfidence() float64                  { return l.Confidence }
func (l *LeafNode) GetNodeType() string                     { return "leaf" }

func (l *LeafNode) Validate() error {
	if l.Prediction == nil {
		return fmt.Errorf("leaf node must have a prediction")
	}
	if l.Samples <= 0 {
		return fmt.Errorf("leaf node must have positive sample count")
	}
	if l.Confidence < 0 || l.Confidence > 1 {
		return fmt.Errorf("confidence must be between 0 and 1, got %.2f", l.Confidence)
	}
	return nil
}

// ToSerializable converts LeafNode to serializable format
func (l *LeafNode) ToSerializable() *SerializableNode {
	// Convert interface{} keys to strings for JSON compatibility
	stringDist := make(map[string]int)
	for class, count := range l.ClassDistribution {
		stringDist[fmt.Sprintf("%v", class)] = count
	}
	
	return &SerializableNode{
		Type:              "leaf",
		Prediction:        l.Prediction,
		ClassDistribution: stringDist,
		Samples:          l.Samples,
		Confidence:       l.Confidence,
	}
}
```

## Decision Node {#decision-node}

```go
// DecisionNode represents an internal node that splits data
type DecisionNode struct {
	Feature           *Feature              `json:"feature"`
	SplitValue        interface{}           `json:"split_value"`        // Threshold for numeric, specific value for categorical
	Children          map[interface{}]Node  `json:"children"`           // Unified children storage
	ClassDistribution map[interface{}]int   `json:"class_distribution"`
	Samples          int                   `json:"samples"`
	Confidence       float64               `json:"confidence"`
}

// NewDecisionNode creates a decision node for any feature type
func NewDecisionNode(feature *Feature, splitValue interface{}, classDistribution map[interface{}]int) (*DecisionNode, error) {
	if feature == nil {
		return nil, fmt.Errorf("decision node requires a feature")
	}
	
	if splitValue == nil {
		return nil, fmt.Errorf("decision node requires a split value")
	}
	
	totalSamples := 0
	majorityClass := interface{}(nil)
	maxCount := 0
	
	for class, count := range classDistribution {
		totalSamples += count
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}
	
	confidence := 0.0
	if totalSamples > 0 {
		confidence = float64(maxCount) / float64(totalSamples)
	}
	
	return &DecisionNode{
		Feature:           feature,
		SplitValue:        splitValue,
		Children:          make(map[interface{}]Node),
		ClassDistribution: classDistribution,
		Samples:          totalSamples,
		Confidence:       confidence,
	}, nil
}

func (d *DecisionNode) IsLeaf() bool                            { return false }
func (d *DecisionNode) GetSamples() int                         { return d.Samples }
func (d *DecisionNode) GetClassDistribution() map[interface{}]int { return d.ClassDistribution }
func (d *DecisionNode) GetMajorityClass() interface{} {
	maxCount := 0
	var majorityClass interface{}
	for class, count := range d.ClassDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}
	return majorityClass
}
func (d *DecisionNode) GetConfidence() float64 { return d.Confidence }
func (d *DecisionNode) GetNodeType() string    { return "decision" }

func (d *DecisionNode) Validate() error {
	if d.Feature == nil {
		return fmt.Errorf("decision node must have a feature")
	}
	if len(d.Children) == 0 {
		return fmt.Errorf("decision node must have at least one child")
	}
	return nil
}

// ToSerializable converts DecisionNode to serializable format
func (d *DecisionNode) ToSerializable() *SerializableNode {
	// Convert interface{} keys to strings for JSON compatibility
	stringDist := make(map[string]int)
	for class, count := range d.ClassDistribution {
		stringDist[fmt.Sprintf("%v", class)] = count
	}
	
	// Convert children to serializable format
	serializedChildren := make(map[string]*SerializableNode)
	for branchValue, child := range d.Children {
		key := fmt.Sprintf("%v", branchValue)
		serializedChildren[key] = child.ToSerializable()
	}
	
	return &SerializableNode{
		Type:              "decision",
		Feature:           d.Feature,
		SplitValue:        d.SplitValue,
		Children:          serializedChildren,
		ClassDistribution: stringDist,
		Samples:          d.Samples,
		Confidence:       d.Confidence,
	}
}


// SetChild sets a child for any split type (unified approach)
func (d *DecisionNode) SetChild(branchValue interface{}, child Node) error {
	if d.IsLeaf() {
		return fmt.Errorf("cannot set children on leaf node")
	}
	if child == nil {
		return fmt.Errorf("child cannot be nil")
	}
	
	d.Children[branchValue] = child
	return nil
}

// GetChild retrieves a child by branch value
func (d *DecisionNode) GetChild(branchValue interface{}) Node {
	return d.Children[branchValue]
}

// GetAllChildren returns all child nodes
func (d *DecisionNode) GetAllChildren() []Node {
	children := make([]Node, 0, len(d.Children))
	for _, child := range d.Children {
		children = append(children, child)
	}
	return children
}

// ====================
// Branch Value Constants for Binary Splits
// ====================

const (
	LeftBranch  = "left"   // For <= threshold
	RightBranch = "right"  // For > threshold
)

// SetLeftChild convenience method for numeric splits
func (d *DecisionNode) SetLeftChild(child Node) error {
	return d.SetChild(LeftBranch, child)
}

// SetRightChild convenience method for numeric splits  
func (d *DecisionNode) SetRightChild(child Node) error {
	return d.SetChild(RightBranch, child)
}

// GetLeftChild convenience method for numeric splits
func (d *DecisionNode) GetLeftChild() Node {
	return d.GetChild(LeftBranch)
}

// GetRightChild convenience method for numeric splits
func (d *DecisionNode) GetRightChild() Node {
	return d.GetChild(RightBranch)
}
```

## Example Numeric Decision Split {#example-numeric-decision-split}

```go
// ExampleNumericSplit demonstrates numeric feature splitting
func ExampleNumericSplit() {
	// Create age feature
	ageFeature := &Feature{Name: "age", Type: NumericFeature}
	
	// Create decision node: "age <= 30"
	distribution := map[interface{}]int{"young": 15, "old": 5}
	ageNode, _ := NewDecisionNode(ageFeature, 30.0, distribution)
	
	// Create children
	leftDist := map[interface{}]int{"young": 12, "old": 1}   // age <= 30
	rightDist := map[interface{}]int{"young": 3, "old": 4}   // age > 30
	
	leftChild, _ := NewLeafNode(leftDist)   // Prediction: "young"
	rightChild, _ := NewLeafNode(rightDist) // Prediction: "old"
	
	// Set children using convenience methods
	ageNode.SetLeftChild(leftChild)
	ageNode.SetRightChild(rightChild)
	
	fmt.Printf("Age node children: left=%v, right=%v\n", 
		ageNode.GetLeftChild().GetMajorityClass(),
		ageNode.GetRightChild().GetMajorityClass())
}
```

## Example Nominal Decision Split {#example-nominal-decision-split}

```go
// ExampleCategoricalSplit demonstrates categorical feature splitting
func ExampleCategoricalSplit() {
	// Create education feature
	educationFeature := &Feature{Name: "education", Type: CategoricalFeature}
	
	// Create decision node for education
	distribution := map[interface{}]int{"approved": 10, "rejected": 8}
	eduNode, _ := NewDecisionNode(educationFeature, "categorical_split", distribution)
	
	// Create children for each education level
	collegeDist := map[interface{}]int{"approved": 7, "rejected": 1}
	highSchoolDist := map[interface{}]int{"approved": 2, "rejected": 4}
	graduateDist := map[interface{}]int{"approved": 1, "rejected": 3}
	
	collegeChild, _ := NewLeafNode(collegeDist)
	highSchoolChild, _ := NewLeafNode(highSchoolDist)
	graduateChild, _ := NewLeafNode(graduateDist)
	
	// Set children using actual category values
	eduNode.SetChild("college", collegeChild)
	eduNode.SetChild("high_school", highSchoolChild)
	eduNode.SetChild("graduate", graduateChild)
	
	fmt.Printf("Education node has %d children\n", len(eduNode.Children))
	fmt.Printf("College prediction: %v\n", eduNode.GetChild("college").GetMajorityClass())
	fmt.Printf("High school prediction: %v\n", eduNode.GetChild("high_school").GetMajorityClass())
	fmt.Printf("Graduate prediction: %v\n", eduNode.GetChild("graduate").GetMajorityClass())
}
```

## Node Serialization to JSON {#node-serialization-to-json}

```go
// SerializeTree converts a tree to JSON
func SerializeTree(root Node) ([]byte, error) {
	if root == nil {
		return nil, fmt.Errorf("cannot serialize nil tree")
	}
	
	serializable := root.ToSerializable()
	return json.MarshalIndent(serializable, "", "  ")
}

// DeserializeTree converts JSON back to tree structure
func DeserializeTree(data []byte) (Node, error) {
	var serializable SerializableNode
	if err := json.Unmarshal(data, &serializable); err != nil {
		return nil, fmt.Errorf("unmarshal JSON: %w", err)
	}
	
	return fromSerializable(&serializable)
}

// fromSerializable converts SerializableNode back to Node interface
func fromSerializable(s *SerializableNode) (Node, error) {
	if s == nil {
		return nil, fmt.Errorf("serializable node cannot be nil")
	}
	
	// Convert string keys back to interface{} for class distribution
	classDist := make(map[interface{}]int)
	for class, count := range s.ClassDistribution {
		classDist[class] = count
	}
	
	switch s.Type {
	case "leaf":
		return &LeafNode{
			Prediction:        s.Prediction,
			ClassDistribution: classDist,
			Samples:          s.Samples,
			Confidence:       s.Confidence,
		}, nil
		
	case "decision":
		// Recreate children recursively
		children := make(map[interface{}]Node)
		for branchKey, childSerializable := range s.Children {
			child, err := fromSerializable(childSerializable)
			if err != nil {
				return nil, fmt.Errorf("deserializing child %s: %w", branchKey, err)
			}
			children[branchKey] = child
		}
		
		return &DecisionNode{
			Feature:           s.Feature,
			SplitValue:        s.SplitValue,
			Children:          children,
			ClassDistribution: classDist,
			Samples:          s.Samples,
			Confidence:       s.Confidence,
		}, nil
		
	default:
		return nil, fmt.Errorf("unknown node type: %s", s.Type)
	}
}
```

## DecisionTree & TreeMetadata {#decisiontree-&-treemetadata}

```go
// DecisionTree represents the complete tree with metadata
type DecisionTree struct {
	Root            Node       `json:"-"`                    // Not directly serialized
	SerializableRoot *SerializableNode `json:"root"`        // For JSON serialization
	Features        []*Feature `json:"features"`             // Feature metadata
	Classes         []string   `json:"classes"`              // Target classes
	Metadata        TreeMetadata `json:"metadata"`           // Tree metadata
}

// TreeMetadata contains information about the tree
type TreeMetadata struct {
	Version        string    `json:"version"`
	CreatedAt      time.Time `json:"created_at"`
	Algorithm      string    `json:"algorithm"`
	MaxDepth       int       `json:"max_depth"`
	MinSamples     int       `json:"min_samples"`
	Criterion      string    `json:"criterion"`
	TotalNodes     int       `json:"total_nodes"`
	LeafNodes      int       `json:"leaf_nodes"`
	TrainingSamples int      `json:"training_samples"`
}

// NewDecisionTree creates a decision tree with metadata
func NewDecisionTree(root Node, features []*Feature, classes []string, metadata TreeMetadata) *DecisionTree {
	tree := &DecisionTree{
		Root:     root,
		Features: features,
		Classes:  classes,
		Metadata: metadata,
	}
	
	// Update statistics
	tree.updateStatistics()
	
	return tree
}

// updateStatistics calculates tree statistics
func (dt *DecisionTree) updateStatistics() {
	if dt.Root != nil {
		dt.Metadata.TotalNodes = CountNodes(dt.Root)
		dt.Metadata.LeafNodes = CountLeaves(dt.Root)
	}
}

```

## Tree Serialization and De-Serialization  {#tree-serialization-and-de-serialization}

```go
// MarshalJSON implements custom JSON marshaling for DecisionTree
func (dt *DecisionTree) MarshalJSON() ([]byte, error) {
	// Convert root to serializable format
	if dt.Root != nil {
		dt.SerializableRoot = dt.Root.ToSerializable()
	}
	
	// Create temporary struct for marshaling
	type Alias DecisionTree
	return json.MarshalIndent((*Alias)(dt), "", "  ")
}

// UnmarshalJSON implements custom JSON unmarshaling for DecisionTree
func (dt *DecisionTree) UnmarshalJSON(data []byte) error {
	// Create temporary struct for unmarshaling
	type Alias DecisionTree
	aux := (*Alias)(dt)
	
	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}
	
	// Convert serializable root back to Node interface
	if dt.SerializableRoot != nil {
		root, err := fromSerializable(dt.SerializableRoot)
		if err != nil {
			return fmt.Errorf("deserializing root: %w", err)
		}
		dt.Root = root
	}
	
	return nil
}

// SaveToFile saves the tree to a JSON file
func (dt *DecisionTree) SaveToFile(filename string) error {
	data, err := json.MarshalIndent(dt, "", "  ")
	if err != nil {
		return fmt.Errorf("marshal tree: %w", err)
	}
	
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("write file: %w", err)
	}
	
	return nil
}

// LoadFromFile loads a tree from a JSON file
func LoadFromFile(filename string) (*DecisionTree, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("read file: %w", err)
	}
	
	var tree DecisionTree
	if err := json.Unmarshal(data, &tree); err != nil {
		return nil, fmt.Errorf("unmarshal tree: %w", err)
	}
	
	return &tree, nil
}
```

## Simplify Config and Parameters {#simplify-config-and-parameters}

We will focus on a limited set of parameters and only introduce new parameters in future as needed. In addition to the based arguments provided in the original specification we will only support three additional parameters.

* **max\_depth** : default \= 10, the maximum depth of a decision tree. When max\_depth is reached, for a particular branch/path, we stop addition of a new code, and create a leaf node assigning the most popular ‘class’ within the subset of samples at this point as the prediction/label at the leaf node.  
* **min\_samples\_split**: default \= 20, the minimum number of instances of samples at a node to create a splitting node. If num\_samples are less than the set value, we will simply create a leaf node and assign the most populate ‘class’ within the samples as the prediction/label for the leaf node.

These values will allow us to control the depth of a decision tree proactively. In future we will add post-pruning features when appropriate, as a separate command \- train, predict, prune. 

The way we will handle this is to start by defining a Config struct, and initializing the default values in code. When we parse command line flags, we override the values in Config after validating it.

Since this will not be running as an always on service, there is no need to create complex caching, resource cleaning up log rotation logic. By default we will initialize **mulberri-train.log** file and **mulberri-predict.log** in the current working directory each time the application is evoked. This means each time we will override previous log files if around. Our Berrijam AI wrapper code will be responsible for preserving and paring the log files as necessary. 

Logging statements will be **Info** by default. If the user has specified the \`**\--verbose**\` flag, then the logging levels will be set to **Debug**.

```go
// Config holds all CLI arguments and their values
type Config struct {
	// Core command
	Command string // "train", "predict"
	
	// Common flags
	InputFile    string // -i, --input
	OutputFile   string // -o, --output
	Verbose      bool   // --verbose
	Help         bool   // --help
	Version      bool   // --version
	
	// Training-specific flags
	TargetColumn    string // -t, --target
	FeatureInfoFile string // -f, --features
	MaxDepth        int    // --max-depth
	MinSamples      int    // --min-samples
	Criterion       string // --criterion
	
	// Prediction-specific flags
	ModelFile string // -m, --model
}

// ====================
// Default Values
// ====================

// GetDefaults returns Config with default values
func GetDefaults() Config {
	return Config{
		Command:         "",           // Required
		InputFile:       "",           // Required
		OutputFile:      "",           // Will be set based on command
		Verbose:         false,
		Help:            false,
		Version:         false,
		TargetColumn:    "",           // Required for training
		FeatureInfoFile: "",           // Optional
		MaxDepth:        10,           // Default tree depth
		MinSamples:      20,           // Default minimum samples
		Criterion:       "entropy",    // Default splitting criterion
		ModelFile:       "",           // Required for prediction
	}
}

// ====================
// CLI Parser
// ====================

// ParseArgs parses command line arguments and returns Config
func ParseArgs() (*Config, error) {
	config := GetDefaults()
	
	// Define flags with default values
	var (
		command         = flag.String("c", "", "Command to execute (train|predict)")
		inputFile       = flag.String("i", "", "Input CSV file path")
		outputFile      = flag.String("o", "", "Output file path")
		verbose         = flag.Bool("verbose", false, "Enable verbose output")
		help            = flag.Bool("help", false, "Show help information")
		version         = flag.Bool("version", false, "Show version information")
		
		// Training flags
		targetColumn    = flag.String("t", "", "Target column name for training")
		featureInfoFile = flag.String("f", "", "Feature info YAML file path")
		maxDepth        = flag.Int("max-depth", 10, "Maximum tree depth")
		minSamples      = flag.Int("min-samples", 20, "Minimum samples required to split")
		criterion       = flag.String("criterion", "entropy", "Split criterion (entropy|gini|mse)")
		
		// Prediction flags
		modelFile       = flag.String("m", "", "Model file path for prediction")
	)
	
	// Custom usage message
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Mulberri - High-performance decision tree toolkit\n\n")
		fmt.Fprintf(os.Stderr, "USAGE:\n")
		fmt.Fprintf(os.Stderr, "  Training:   mulberri -c train -i <data.csv> -t <target> -o <model.dt> [options]\n")
		fmt.Fprintf(os.Stderr, "  Prediction: mulberri -c predict -i <data.csv> -m <model.dt> -o <predictions.csv> [options]\n\n")
		fmt.Fprintf(os.Stderr, "FLAGS:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nEXAMPLES:\n")
		fmt.Fprintf(os.Stderr, "  # Basic training\n")
		fmt.Fprintf(os.Stderr, "  mulberri -c train -i data.csv -t species -o model.dt\n\n")
		fmt.Fprintf(os.Stderr, "  # Advanced training\n")
		fmt.Fprintf(os.Stderr, "  mulberri -c train -i data.csv -t species -o model.dt -f features.yaml --max-depth 15 --verbose\n\n")
		fmt.Fprintf(os.Stderr, "  # Making predictions\n")
		fmt.Fprintf(os.Stderr, "  mulberri -c predict -i new_data.csv -m model.dt -o predictions.csv\n\n")
	}
	
	// Parse command line
	flag.Parse()
	
	// Copy parsed values to config
	config.Command = *command
	config.InputFile = *inputFile
	config.OutputFile = *outputFile
	config.Verbose = *verbose
	config.Help = *help
	config.Version = *version
	config.TargetColumn = *targetColumn
	config.FeatureInfoFile = *featureInfoFile
	config.MaxDepth = *maxDepth
	config.MinSamples = *minSamples
	config.Criterion = strings.ToLower(*criterion)
	config.ModelFile = *modelFile
	
	return &config, nil
}

```

## Handle Missing Values {#handle-missing-values}

We will expect data preparation steps prior to calling mulberri decision tree to manage data inconsistency and missing values. This way we can experiment with different data-preparation strategies outside of decision tree training and evaluation. Mulberri will NOT handle missing values by itself. If it encounters missing values when reading the CSV file, it should error out at that point, rather than attempt to build a tree.

## Feature Info YAML {#feature-info-yaml}

Feature info file supplied during training provides us with additional information about columns within the CSV file and how to handle them. We should not impose additional requirements to provide distribution information about the values, considering we can calculate them dynamically globally upon loading the Dataset or will need to for each DatasetView. That is why we will only support the following YAML configuration.

```
feature_name:
   type: type_value
   handle_as : core_type_value

where,
  * type is one of { 'nominal', 'numeric', 'date', datetime', 'binary', 'time'}
  * handle_as is one of { 'integer', 'float', 'string'}
```

Here is an example file.

```
weather:
  type: nominal
  handle_as: string

temperature:
  type: numeric
  handle_as: float

age:
  type: numeric
  handle_as: int

played_on:
  type: datetime
  handle_as: int

play_tennis:
  type: nominal
  hande_as: string
```

When we are parsing the dataset CSV for training, at that point we will see all the unique nominal values to generate a global feature distribution for each feature. This distribution will be saved in the model.json file as part of serializing the decision tree.  
 

## Sample JSON Tree File {#sample-json-tree-file}

```json
{
  "root": {
    "type": "decision",
    "feature": {
      "name": "age", 
      "type": "numeric"
    },
    "split_value": 30.0,
    "children": {
      "left": {
        "type": "decision",
        "feature": {
          "name": "education",
          "type": "categorical"
        },
        "split_value": "categorical_split",
        "children": {
          "college": {
            "type": "leaf",
            "prediction": "approved",
            "class_distribution": {"approved": 5, "rejected": 1},
            "samples": 6,
            "confidence": 0.833
          },
          "high_school": {
            "type": "leaf", 
            "prediction": "approved",
            "class_distribution": {"approved": 2, "rejected": 2},
            "samples": 4,
            "confidence": 0.5
          }
        },
        "class_distribution": {"approved": 7, "rejected": 3},
        "samples": 10,
        "confidence": 0.7
      },
      "right": {
        "type": "leaf",
        "prediction": "rejected", 
        "class_distribution": {"approved": 3, "rejected": 5},
        "samples": 8,
        "confidence": 0.625
      }
    },
    "class_distribution": {"approved": 10, "rejected": 8},
    "samples": 18,
    "confidence": 0.556
  },
  "features": [...],
  "classes": ["approved", "rejected"],
  "metadata": {
    "version": "1.0",
    "algorithm": "C4.5",
    "max_depth": 10,
    "min_samples": 20,
    "total_nodes": 5,
    "leaf_nodes": 3
  }
}
```

## Code Structure for Clarity {#code-structure-for-clarity}

We are also going to change our code layout structure and organize it logically. Everything should go under internal control right now and nothing should go under pkg. Shift to pkg will only happen when we are mature enough to expose any part of our code as a re-usable interface component.  

Everything under **mulberri/internal** should be structured more logically as follows. Note this is a guide, for how to structure, and specific files might shift.

```

internal/
├── config/                    # Global settings for all operations
│   ├── defaults.go            # Default values (min samples, max depth, etc.)
│   ├── training.go            # Training-specific config
│   ├── prediction.go          # Prediction-specific config  
│   ├── evaluation.go          # Evaluation/validation config
│   └── validation.go          # Config validation rules
├── data/                      # Data structures (shared across operations)
│   ├── features/
│   │   ├── feature_info.go       # Feature Type information - Saves only the name, type, distribution (from training data)
│   │   ├── feature_converter.go  # Handles type conversion at lading into float, int or string
│   │   └── loader.go             # YAML feature metadata loader - loads only name and type values for each feature
│   └── dataset/
│       ├── dataset.go
│       ├── dataset_view.go
│       ├── loader.go          # CSV data loading with validation
│       └── validation.go
├── tree/                      # Tree structures (used in all operations)
│   ├── node.go               # Node interface and implementations
│   ├── tree.go               # DecisionTree container
│   └── traversal.go          # Tree navigation for prediction
├── training/                  # Training-specific logic
│   ├── builder.go            # Tree builder orchestration
│   ├── splitter.go           # Split finding interface
│   ├── impurity.go           # Impurity calculations
│   ├── stopping.go           # Stopping criteria evaluation
│   └── evaluators.go         # Split evaluation strategies
├── prediction/                # Prediction-specific logic
│   ├── predictor.go          # Main prediction service
│   ├── batch.go              # Batch prediction handling
│   └── validation.go         # Input validation for prediction
├── evaluation/               # Statistics and measurements on the node and tree level go here
│   ├── metrics.go            # Accuracy, precision, recall, F1
│   ├── confusion_matrix.go   # Confusion matrix calculations
│   ├── node_stats.go         # Node-level statistics
│   ├── tree_stats.go         # Tree-level statistics  
│   └── performance.go        # Performance benchmarking
├── io/                        # I/O operations (used by all workflows)
│   ├── formats/
│   │   ├── csv/
│   │   ├── json/
│   │   └── yaml/
│   ├── persistence/          # Tree save/load
│   └── cli/                  # Command line parsing
└── utils/                    # Pure utilities
    └── logger/

```

Unit Tests will be kept in the same directory for the moment, and named with the **\_test.go** suffix. In future we might migrate them all under the root level **/tests** folder and integrate it as part of the benchmark