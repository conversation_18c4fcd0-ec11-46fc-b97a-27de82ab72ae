#!/usr/bin/env python3
"""
Run benchmark tests on main implementation.

This script runs training on all benchmark datasets using the main Go implementation,
collects performance metrics, and stores results systematically.
"""

import os
import subprocess
import time
import json
import glob
from typing import Dict, List, Any
import yaml


def get_dataset_info(data_dir: str) -> List[Dict[str, str]]:
    """Get information about available datasets."""
    datasets = []
    
    # Find all training CSV files
    train_files = glob.glob(os.path.join(data_dir, "*_train.csv"))
    
    for train_file in train_files:
        # Extract dataset name
        basename = os.path.basename(train_file)
        dataset_name = basename.replace("_train.csv", "")
        
        # Check for corresponding files
        predict_file = os.path.join(data_dir, f"{dataset_name}_predict.csv")
        actual_file = os.path.join(data_dir, f"{dataset_name}_actual.csv")
        metadata_file = os.path.join(data_dir, f"{dataset_name}_metadata.yaml")
        
        if all(os.path.exists(f) for f in [predict_file, actual_file, metadata_file]):
            datasets.append({
                'name': dataset_name,
                'train': train_file,
                'predict': predict_file,
                'actual': actual_file,
                'metadata': metadata_file
            })
    
    return sorted(datasets, key=lambda x: x['name'])


def get_target_column(metadata_file: str) -> str:
    """Extract target column from metadata file."""
    with open(metadata_file, 'r') as f:
        metadata = yaml.safe_load(f)
    
    # Look for common target column names
    target_candidates = ['y', 'target', 'class', 'label', 'outcome']
    
    for candidate in target_candidates:
        if candidate in metadata:
            return candidate
    
    # If no common names found, return the last column (common convention)
    columns = list(metadata.keys())
    return columns[-1] if columns else 'y'


def run_training(dataset: Dict[str, str], output_dir: str, mulberri_path: str) -> Dict[str, Any]:
    """Run training on a single dataset and collect metrics."""
    dataset_name = dataset['name']
    print(f"\n=== Training on {dataset_name} ===")
    
    # Get target column
    target_col = get_target_column(dataset['metadata'])
    print(f"Target column: {target_col}")
    
    # Prepare output paths
    model_file = os.path.join(output_dir, f"{dataset_name}_main_model.dt")
    
    # Build training command (main branch uses -c flag)
    cmd = [
        mulberri_path, "-c", "train",
        "-i", dataset['train'],
        "-t", target_col,
        "-o", model_file,
        "-f", dataset['metadata'],
        "--max-depth", "10",
        "--min-samples", "20",
        "--verbose"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    # Run training and measure time
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        training_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"Training failed for {dataset_name}")
            print(f"Error: {result.stderr}")
            return {
                'dataset': dataset_name,
                'status': 'failed',
                'error': result.stderr,
                'training_time': training_time
            }
        
        # Parse training output for tree statistics
        output_lines = result.stdout.split('\n')
        tree_stats = parse_training_output(output_lines)
        
        # Load the saved model to get additional info
        model_info = {}
        if os.path.exists(model_file):
            try:
                with open(model_file, 'r') as f:
                    model_data = json.load(f)
                    model_info = extract_model_info(model_data)
            except Exception as e:
                print(f"Warning: Could not parse model file: {e}")
        
        result_data = {
            'dataset': dataset_name,
            'status': 'success',
            'training_time': training_time,
            'model_file': model_file,
            'target_column': target_col,
            **tree_stats,
            **model_info
        }
        
        print(f"Training completed successfully in {training_time:.2f}s")
        print(f"Tree stats: {tree_stats}")
        
        return result_data
        
    except subprocess.TimeoutExpired:
        return {
            'dataset': dataset_name,
            'status': 'timeout',
            'training_time': 300.0
        }
    except Exception as e:
        return {
            'dataset': dataset_name,
            'status': 'error',
            'error': str(e),
            'training_time': time.time() - start_time
        }


def parse_training_output(output_lines: List[str]) -> Dict[str, Any]:
    """Parse training output to extract tree statistics."""
    stats = {}
    
    for line in output_lines:
        if "Tree construction completed:" in line:
            # Extract: "Tree construction completed: 62 nodes, 32 leaves, depth 11"
            parts = line.split(":")
            if len(parts) > 1:
                info = parts[1].strip()
                # Parse numbers
                tokens = info.split()
                for i, token in enumerate(tokens):
                    if token.isdigit():
                        if i+1 < len(tokens):
                            if tokens[i+1] == "nodes":
                                stats['total_nodes'] = int(token)
                            elif tokens[i+1] == "leaves":
                                stats['leaf_nodes'] = int(token)
                            elif tokens[i+1] == "depth":
                                stats['max_depth'] = int(token)
        
        elif "Successfully built tree with" in line:
            # Extract: "Successfully built tree with 62 nodes, 32 leaves, depth 11"
            tokens = line.split()
            for i, token in enumerate(tokens):
                if token.isdigit():
                    if i+1 < len(tokens):
                        if tokens[i+1] == "nodes":
                            stats['total_nodes'] = int(token)
                        elif tokens[i+1] == "leaves":
                            stats['leaf_nodes'] = int(token)
                        elif tokens[i+1] == "depth":
                            stats['max_depth'] = int(token)
        
        elif "Loaded" in line and "training samples" in line:
            # Extract: "Loaded 3616 training samples with 16 features"
            parts = line.split()
            for i, part in enumerate(parts):
                if part.isdigit():
                    if "samples" in parts[i+1:i+3]:
                        stats['training_samples'] = int(part)
                    elif "features" in parts[i+1:i+3]:
                        stats['num_features'] = int(part)
    
    return stats


def extract_model_info(model_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract additional information from saved model."""
    info = {}

    # Main branch stores statistics directly in the root
    info['total_nodes'] = model_data.get('node_count', 0)
    info['leaf_nodes'] = model_data.get('leaf_count', 0)
    info['max_depth'] = model_data.get('depth', 0)

    if 'features' in model_data:
        info['model_features'] = len(model_data['features'])

    if 'classes' in model_data:
        info['model_classes'] = len(model_data['classes'])

    if 'config' in model_data:
        config = model_data['config']
        info['algorithm'] = 'C4.5'
        info['criterion'] = config.get('criterion', 'unknown')
        info['model_max_depth'] = config.get('max_depth', 0)
        info['model_min_samples'] = config.get('min_samples', 0)

    return info


def main():
    """Main function to run benchmark on all datasets."""
    # Configuration
    data_dir = "benchmark/data"
    output_dir = "results_main"
    mulberri_path = "./bin/mulberri_main"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Check if mulberri binary exists
    if not os.path.exists(mulberri_path):
        print(f"Error: {mulberri_path} not found. Please build the binary first.")
        return
    
    # Get available datasets
    datasets = get_dataset_info(data_dir)
    print(f"Found {len(datasets)} datasets:")
    for dataset in datasets:
        print(f"  - {dataset['name']}")
    
    if not datasets:
        print("No datasets found!")
        return
    
    # Run training on each dataset
    results = []
    for dataset in datasets:
        result = run_training(dataset, output_dir, mulberri_path)
        results.append(result)
    
    # Save results summary
    results_file = os.path.join(output_dir, "main_training_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Create summary report
    create_summary_report(results, output_dir)
    
    print(f"\n=== Benchmark Complete ===")
    print(f"Results saved to: {output_dir}")
    print(f"Summary: {results_file}")


def create_summary_report(results: List[Dict[str, Any]], output_dir: str):
    """Create a summary report of the benchmark results."""
    summary_file = os.path.join(output_dir, "main_summary.txt")
    
    with open(summary_file, 'w') as f:
        f.write("Main Branch Benchmark Summary\n")
        f.write("=" * 50 + "\n\n")
        
        successful = [r for r in results if r.get('status') == 'success']
        failed = [r for r in results if r.get('status') != 'success']
        
        f.write(f"Total datasets: {len(results)}\n")
        f.write(f"Successful: {len(successful)}\n")
        f.write(f"Failed: {len(failed)}\n\n")
        
        if failed:
            f.write("Failed datasets:\n")
            for result in failed:
                f.write(f"  - {result['dataset']}: {result.get('status', 'unknown')}\n")
            f.write("\n")
        
        if successful:
            f.write("Successful datasets:\n")
            f.write("-" * 30 + "\n")
            for result in successful:
                f.write(f"Dataset: {result['dataset']}\n")
                f.write(f"  Training time: {result.get('training_time', 0):.2f}s\n")
                f.write(f"  Total nodes: {result.get('total_nodes', 'N/A')}\n")
                f.write(f"  Leaf nodes: {result.get('leaf_nodes', 'N/A')}\n")
                f.write(f"  Max depth: {result.get('max_depth', 'N/A')}\n")
                f.write(f"  Training samples: {result.get('training_samples', 'N/A')}\n")
                f.write(f"  Features: {result.get('num_features', 'N/A')}\n")
                f.write("\n")
    
    print(f"Summary report saved to: {summary_file}")


if __name__ == "__main__":
    main()
