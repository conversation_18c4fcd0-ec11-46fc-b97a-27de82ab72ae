#!/usr/bin/env python3
"""
Run benchmark tests on Python C4.5 implementation.

This script runs training on all benchmark datasets using the Python C4.5 implementation,
collects performance metrics, and stores results systematically.
"""

import os
import subprocess
import time
import json
import glob
from typing import Dict, List, Any
import yaml


def get_dataset_info(data_dir: str) -> List[Dict[str, str]]:
    """Get information about available datasets."""
    datasets = []
    
    # Find all training CSV files
    train_files = glob.glob(os.path.join(data_dir, "*_train.csv"))
    
    for train_file in train_files:
        # Extract dataset name
        basename = os.path.basename(train_file)
        dataset_name = basename.replace("_train.csv", "")
        
        # Check for corresponding files
        predict_file = os.path.join(data_dir, f"{dataset_name}_predict.csv")
        actual_file = os.path.join(data_dir, f"{dataset_name}_actual.csv")
        metadata_file = os.path.join(data_dir, f"{dataset_name}_metadata.yaml")
        
        if all(os.path.exists(f) for f in [predict_file, actual_file, metadata_file]):
            datasets.append({
                'name': dataset_name,
                'train': train_file,
                'predict': predict_file,
                'actual': actual_file,
                'metadata': metadata_file
            })
    
    return sorted(datasets, key=lambda x: x['name'])


def get_target_column(metadata_file: str) -> str:
    """Extract target column from metadata file."""
    with open(metadata_file, 'r') as f:
        metadata = yaml.safe_load(f)
    
    # Look for common target column names
    target_candidates = ['y', 'target', 'class', 'label', 'outcome']
    
    for candidate in target_candidates:
        if candidate in metadata:
            return candidate
    
    # If no common names found, return the last column (common convention)
    columns = list(metadata.keys())
    return columns[-1] if columns else 'y'


def run_training(dataset: Dict[str, str], output_dir: str, dt_path: str) -> Dict[str, Any]:
    """Run training on a single dataset and collect metrics."""
    dataset_name = dataset['name']
    print(f"\n=== Training on {dataset_name} ===")
    
    # Get target column
    target_col = get_target_column(dataset['metadata'])
    print(f"Target column: {target_col}")
    
    # Prepare output paths
    model_file = os.path.join(output_dir, f"{dataset_name}_python_model.json")
    
    # Build training command (Python dt.py)
    cmd = [
        "python3", dt_path,
        "-c", "train",
        "-i", dataset['train'],
        "-t", target_col,
        "-o", model_file,
        "--metadata", dataset['metadata'],
        "--n-jobs", "2"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    # Run training and measure time
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, cwd="benchmark")
        training_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"Training failed for {dataset_name}")
            print(f"Error: {result.stderr}")
            return {
                'dataset': dataset_name,
                'status': 'failed',
                'error': result.stderr,
                'training_time': training_time
            }
        
        # Load the saved model to get tree info
        model_info = {}
        if os.path.exists(model_file):
            try:
                with open(model_file, 'r') as f:
                    model_data = json.load(f)
                    model_info = extract_model_info(model_data)
            except Exception as e:
                print(f"Warning: Could not parse model file: {e}")
        
        result_data = {
            'dataset': dataset_name,
            'status': 'success',
            'training_time': training_time,
            'model_file': model_file,
            'target_column': target_col,
            **model_info
        }
        
        print(f"Training completed successfully in {training_time:.2f}s")
        print(f"Tree stats: {model_info}")
        
        return result_data
        
    except subprocess.TimeoutExpired:
        return {
            'dataset': dataset_name,
            'status': 'timeout',
            'training_time': 300.0
        }
    except Exception as e:
        return {
            'dataset': dataset_name,
            'status': 'error',
            'error': str(e),
            'training_time': time.time() - start_time
        }


def extract_model_info(model_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract additional information from saved Python model."""
    info = {}
    
    # Python implementation stores tree info differently
    if 'tree_info' in model_data:
        tree_info = model_data['tree_info']
        info['total_nodes'] = tree_info.get('total_nodes', 0)
        info['leaf_nodes'] = tree_info.get('leaf_nodes', 0)
        info['max_depth'] = tree_info.get('actual_depth', 0)
    
    if 'config' in model_data:
        config = model_data['config']
        info['algorithm'] = 'Python C4.5'
        info['model_max_depth'] = config.get('max_depth', 0)
        info['model_min_instances_pc'] = config.get('min_instances_pc', 0)
        info['enable_pruning'] = config.get('enable_pruning', False)
    
    if 'feature_types' in model_data:
        info['model_features'] = len(model_data['feature_types'])
    
    if 'target_type' in model_data:
        info['target_type'] = model_data['target_type']
    
    return info


def main():
    """Main function to run benchmark on all datasets."""
    # Configuration
    data_dir = "benchmark/data"
    output_dir = "results_python"
    dt_path = "dt.py"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Check if dt.py exists
    if not os.path.exists(os.path.join("benchmark", dt_path)):
        print(f"Error: benchmark/{dt_path} not found.")
        return
    
    # Get available datasets
    datasets = get_dataset_info(data_dir)
    print(f"Found {len(datasets)} datasets:")
    for dataset in datasets:
        print(f"  - {dataset['name']}")
    
    if not datasets:
        print("No datasets found!")
        return
    
    # Run training on each dataset
    results = []
    for dataset in datasets:
        result = run_training(dataset, output_dir, dt_path)
        results.append(result)
    
    # Save results summary
    results_file = os.path.join(output_dir, "python_training_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Create summary report
    create_summary_report(results, output_dir)
    
    print(f"\n=== Benchmark Complete ===")
    print(f"Results saved to: {output_dir}")
    print(f"Summary: {results_file}")


def create_summary_report(results: List[Dict[str, Any]], output_dir: str):
    """Create a summary report of the benchmark results."""
    summary_file = os.path.join(output_dir, "python_summary.txt")
    
    with open(summary_file, 'w') as f:
        f.write("Python C4.5 Benchmark Summary\n")
        f.write("=" * 50 + "\n\n")
        
        successful = [r for r in results if r.get('status') == 'success']
        failed = [r for r in results if r.get('status') != 'success']
        
        f.write(f"Total datasets: {len(results)}\n")
        f.write(f"Successful: {len(successful)}\n")
        f.write(f"Failed: {len(failed)}\n\n")
        
        if failed:
            f.write("Failed datasets:\n")
            for result in failed:
                f.write(f"  - {result['dataset']}: {result.get('status', 'unknown')}\n")
            f.write("\n")
        
        if successful:
            f.write("Successful datasets:\n")
            f.write("-" * 30 + "\n")
            for result in successful:
                f.write(f"Dataset: {result['dataset']}\n")
                f.write(f"  Training time: {result.get('training_time', 0):.2f}s\n")
                f.write(f"  Total nodes: {result.get('total_nodes', 'N/A')}\n")
                f.write(f"  Leaf nodes: {result.get('leaf_nodes', 'N/A')}\n")
                f.write(f"  Max depth: {result.get('max_depth', 'N/A')}\n")
                f.write(f"  Algorithm: {result.get('algorithm', 'N/A')}\n")
                f.write(f"  Pruning: {result.get('enable_pruning', 'N/A')}\n")
                f.write("\n")
    
    print(f"Summary report saved to: {summary_file}")


if __name__ == "__main__":
    main()
